#!/usr/bin/env python3
"""
SIH Excel Sorter - Sorts SIH problem statements by predicted competition level
Takes Excel file and outputs sorted results with predictions
"""

import pandas as pd
import numpy as np
from ultra_accurate_predictor import UltraAccurateSIHPredictor
import warnings
warnings.filterwarnings('ignore')

class SIHExcelSorter:
    def __init__(self):
        self.predictor = None
        self.is_trained = False
        
    def load_and_train_predictor(self):
        """Load and train the ultra-accurate predictor"""
        print("🚀 Loading Ultra-Accurate SIH Predictor...")
        print("⏳ This may take a few minutes for maximum accuracy...")
        
        self.predictor = UltraAccurateSIHPredictor()
        results = self.predictor.train_ultra_model()
        
        self.is_trained = True
        print(f"✅ Model trained with {results['accuracy_percent']:.1f}% accuracy!")
        
        return results
    
    def process_excel_file(self, excel_path, output_path=None):
        """Process Excel file and sort by predicted competition"""
        if not self.is_trained:
            print("🔧 Training predictor first...")
            self.load_and_train_predictor()
        
        print(f"\n📊 Processing Excel file: {excel_path}")
        
        # Load Excel file
        try:
            df = pd.read_excel(excel_path)
            print(f"✅ Loaded {len(df)} problem statements")
        except Exception as e:
            print(f"❌ Error loading Excel file: {e}")
            return None
        
        # Identify columns
        title_col = self._find_title_column(df)
        org_col = self._find_organization_column(df)
        category_col = self._find_category_column(df)
        domain_col = self._find_domain_column(df)
        
        if title_col is None:
            print("❌ Could not find title column in Excel file")
            return None
        
        print(f"📝 Using columns:")
        print(f"   Title: {title_col}")
        print(f"   Organization: {org_col or 'Not found - using default'}")
        print(f"   Category: {category_col or 'Not found - using default'}")
        print(f"   Domain: {domain_col or 'Not found - using default'}")
        
        # Make predictions
        print(f"\n🔮 Making predictions...")
        predictions = []
        competition_levels = []
        recommendations = []
        
        for idx, row in df.iterrows():
            try:
                title = str(row[title_col]) if pd.notna(row[title_col]) else "Unknown"
                organization = str(row[org_col]) if org_col and pd.notna(row[org_col]) else "Unknown"
                category = str(row[category_col]) if category_col and pd.notna(row[category_col]) else "Software"
                domain = str(row[domain_col]) if domain_col and pd.notna(row[domain_col]) else "Miscellaneous"
                
                # Make prediction
                pred_count = self.predictor.predict_submission_count(title, organization, category, domain)
                predictions.append(pred_count)
                
                # Determine competition level
                if pred_count < 30:
                    level = "🟢 VERY LOW"
                    recommendation = "✅ EXCELLENT CHOICE - Apply immediately!"
                elif pred_count < 60:
                    level = "🟡 LOW"
                    recommendation = "👍 GOOD OPPORTUNITY - High success chance"
                elif pred_count < 100:
                    level = "🟠 MEDIUM"
                    recommendation = "⚠️ MODERATE COMPETITION - Need strong solution"
                elif pred_count < 150:
                    level = "🔴 HIGH"
                    recommendation = "❌ HIGH COMPETITION - Consider alternatives"
                else:
                    level = "⚫ VERY HIGH"
                    recommendation = "🚫 AVOID - Extremely high competition"
                
                competition_levels.append(level)
                recommendations.append(recommendation)
                
                if (idx + 1) % 10 == 0:
                    print(f"   Processed {idx + 1}/{len(df)} problems...")
                    
            except Exception as e:
                print(f"⚠️ Error predicting for row {idx}: {e}")
                predictions.append(999)  # High number to sort to bottom
                competition_levels.append("❌ ERROR")
                recommendations.append("Error in prediction")
        
        # Add predictions to dataframe
        df['Predicted_Submissions'] = predictions
        df['Competition_Level'] = competition_levels
        df['Recommendation'] = recommendations
        
        # Sort by predicted submissions (low to high)
        df_sorted = df.sort_values('Predicted_Submissions', ascending=True)
        
        # Add ranking
        df_sorted['Rank'] = range(1, len(df_sorted) + 1)
        
        # Reorder columns to put important info first
        cols = ['Rank', 'Predicted_Submissions', 'Competition_Level', 'Recommendation']
        cols.extend([col for col in df_sorted.columns if col not in cols])
        df_sorted = df_sorted[cols]
        
        print(f"\n📊 SORTING RESULTS:")
        print(f"   🟢 Very Low Competition (< 30): {len(df_sorted[df_sorted['Predicted_Submissions'] < 30])}")
        print(f"   🟡 Low Competition (30-60): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 30) & (df_sorted['Predicted_Submissions'] < 60)])}")
        print(f"   🟠 Medium Competition (60-100): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 60) & (df_sorted['Predicted_Submissions'] < 100)])}")
        print(f"   🔴 High Competition (100-150): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 100) & (df_sorted['Predicted_Submissions'] < 150)])}")
        print(f"   ⚫ Very High Competition (150+): {len(df_sorted[df_sorted['Predicted_Submissions'] >= 150])}")
        
        # Save results
        if output_path is None:
            output_path = excel_path.replace('.xlsx', '_SORTED_BY_COMPETITION.xlsx')
        
        try:
            df_sorted.to_excel(output_path, index=False)
            print(f"\n💾 Results saved to: {output_path}")
        except Exception as e:
            print(f"❌ Error saving file: {e}")
            return df_sorted
        
        # Show top 10 best opportunities
        print(f"\n🏆 TOP 10 BEST OPPORTUNITIES (Lowest Competition):")
        print("=" * 80)
        
        top_10 = df_sorted.head(10)
        for idx, row in top_10.iterrows():
            title = str(row[title_col])[:60] + "..." if len(str(row[title_col])) > 60 else str(row[title_col])
            print(f"{row['Rank']:2d}. {title}")
            print(f"    Predicted: {row['Predicted_Submissions']} submissions | {row['Competition_Level']}")
            print(f"    {row['Recommendation']}")
            print()
        
        return df_sorted
    
    def _find_title_column(self, df):
        """Find the title column in the dataframe"""
        possible_names = ['title', 'problem statement', 'problem', 'ps title', 'statement', 'problem statement title']
        
        for col in df.columns:
            if any(name in col.lower() for name in possible_names):
                return col
        
        # If not found, return first column that looks like text
        for col in df.columns:
            if df[col].dtype == 'object' and df[col].str.len().mean() > 20:
                return col
        
        return None
    
    def _find_organization_column(self, df):
        """Find the organization column"""
        possible_names = ['organization', 'org', 'company', 'ministry', 'department']
        
        for col in df.columns:
            if any(name in col.lower() for name in possible_names):
                return col
        return None
    
    def _find_category_column(self, df):
        """Find the category column"""
        possible_names = ['category', 'type', 'software', 'hardware']
        
        for col in df.columns:
            if any(name in col.lower() for name in possible_names):
                return col
        return None
    
    def _find_domain_column(self, df):
        """Find the domain/theme column"""
        possible_names = ['domain', 'theme', 'area', 'field', 'bucket']
        
        for col in df.columns:
            if any(name in col.lower() for name in possible_names):
                return col
        return None

def main():
    """Main function for command line usage"""
    import sys
    
    print("🎯 SIH EXCEL SORTER - STRATEGIC COMPETITION ANALYZER")
    print("=" * 60)
    
    # Get file path
    if len(sys.argv) > 1:
        excel_path = sys.argv[1]
    else:
        excel_path = input("📁 Enter path to Excel file (or press Enter for default): ").strip()
        if not excel_path:
            excel_path = "SIH_PS_2024.xlsx"
    
    # Check if file exists
    import os
    if not os.path.exists(excel_path):
        print(f"❌ File not found: {excel_path}")
        return
    
    # Process file
    sorter = SIHExcelSorter()
    
    try:
        results = sorter.process_excel_file(excel_path)
        
        if results is not None:
            print(f"\n🎉 SUCCESS! Excel file sorted by competition level")
            print(f"📊 {len(results)} problem statements analyzed and ranked")
            print(f"💡 Focus on the top-ranked (low competition) opportunities!")
            print(f"\n🚀 Ready to strategically dominate SIH 2025!")
        else:
            print(f"❌ Failed to process Excel file")
            
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
