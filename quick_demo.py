#!/usr/bin/env python3
"""
Quick Demo of SIH Prediction System
Shows the Excel sorting functionality using the existing trained model
"""

import pandas as pd
import numpy as np
from sih_ml_predictor import SIHMLPredictor
import warnings
warnings.filterwarnings('ignore')

def demo_excel_sorting():
    """Demo the Excel sorting functionality"""
    print("🎯 SIH EXCEL SORTING DEMO")
    print("=" * 50)
    
    # Load the existing trained model
    print("📊 Loading existing trained model...")
    predictor = SIHMLPredictor()
    predictor.load_data()
    predictor.advanced_feature_engineering()
    X, y = predictor.prepare_features_for_ml()
    results = predictor.train_models(X, y)
    
    print(f"✅ Model loaded with {results[predictor.best_model_name]['r2']*100:.1f}% accuracy")
    
    # Check if Excel file exists
    excel_path = "SIH_PS_2024.xlsx"
    try:
        df = pd.read_excel(excel_path)
        print(f"📁 Found Excel file with {len(df)} problem statements")
    except FileNotFoundError:
        print(f"❌ Excel file not found: {excel_path}")
        print("📝 Creating demo data instead...")
        
        # Create demo data
        demo_data = {
            'PS Number': [f'SIH{i:04d}' for i in range(1, 21)],
            'Problem Statement Title': [
                'AI-powered healthcare diagnosis system using machine learning',
                'Blockchain-based supply chain management platform',
                'IoT sensor network for smart agriculture monitoring',
                'Virtual reality educational platform for rural schools',
                'Smart traffic management system using computer vision',
                'Cybersecurity framework for government databases',
                'Mobile app for digital payments in rural areas',
                'Drone-based disaster management and rescue operations',
                'Smart waste management system for cities',
                'Telemedicine platform for remote patient monitoring',
                'E-learning platform with adaptive learning algorithms',
                'Smart grid optimization using renewable energy',
                'Automated crop disease detection using image processing',
                'Digital identity verification system using biometrics',
                'Smart parking management system for urban areas',
                'Water quality monitoring using IoT sensors',
                'Predictive maintenance for industrial equipment',
                'Smart home automation system with voice control',
                'Digital marketplace for local artisans and craftsmen',
                'Mental health support chatbot using natural language processing'
            ],
            'Organization': [
                'AIIMS Delhi', 'Tech Mahindra', 'Ministry of Agriculture', 'Ministry of Education',
                'Smart City Corp', 'DRDO', 'Paytm', 'ISRO', 'Municipal Corporation',
                'Apollo Hospitals', 'BYJU\'S', 'NTPC', 'IIT Bombay', 'Aadhaar Authority',
                'Infosys', 'Jal Shakti Ministry', 'Tata Steel', 'Amazon', 'Flipkart', 'Microsoft'
            ],
            'Category': ['Software'] * 15 + ['Hardware'] * 5,
            'Theme': [
                'MedTech / BioTech / HealthTech', 'Blockchain & Cybersecurity', 'Agriculture, FoodTech & Rural Development',
                'Smart Education', 'Smart Automation', 'Blockchain & Cybersecurity', 'Fintech',
                'Disaster Management', 'Clean & Green Technology', 'MedTech / BioTech / HealthTech',
                'Smart Education', 'Renewable / Sustainable Energy', 'Agriculture, FoodTech & Rural Development',
                'Blockchain & Cybersecurity', 'Smart Automation', 'Clean & Green Technology',
                'Smart Automation', 'Smart Automation', 'Miscellaneous', 'MedTech / BioTech / HealthTech'
            ]
        }
        
        df = pd.DataFrame(demo_data)
        excel_path = "demo_sih_problems.xlsx"
        df.to_excel(excel_path, index=False)
        print(f"📁 Created demo file: {excel_path}")
    
    # Make predictions for all problems
    print(f"\n🔮 Making predictions for all {len(df)} problems...")
    
    predictions = []
    competition_levels = []
    recommendations = []
    
    for idx, row in df.iterrows():
        try:
            # Extract data
            title = str(row.get('Problem Statement Title', row.get('Title', 'Unknown')))
            organization = str(row.get('Organization', 'Unknown'))
            category = str(row.get('Category', 'Software'))
            domain = str(row.get('Theme', row.get('Domain', 'Miscellaneous')))
            
            # Make prediction
            pred_count = predictor.predict_submission_count(title, organization, category, domain)
            predictions.append(pred_count)
            
            # Determine competition level
            if pred_count < 30:
                level = "🟢 VERY LOW"
                recommendation = "✅ EXCELLENT CHOICE - Apply immediately!"
            elif pred_count < 60:
                level = "🟡 LOW"
                recommendation = "👍 GOOD OPPORTUNITY - High success chance"
            elif pred_count < 100:
                level = "🟠 MEDIUM"
                recommendation = "⚠️ MODERATE COMPETITION - Need strong solution"
            elif pred_count < 150:
                level = "🔴 HIGH"
                recommendation = "❌ HIGH COMPETITION - Consider alternatives"
            else:
                level = "⚫ VERY HIGH"
                recommendation = "🚫 AVOID - Extremely high competition"
            
            competition_levels.append(level)
            recommendations.append(recommendation)
            
        except Exception as e:
            print(f"⚠️ Error predicting for row {idx}: {e}")
            predictions.append(999)
            competition_levels.append("❌ ERROR")
            recommendations.append("Error in prediction")
    
    # Add predictions to dataframe
    df['Predicted_Submissions'] = predictions
    df['Competition_Level'] = competition_levels
    df['Recommendation'] = recommendations
    
    # Sort by predicted submissions (low to high)
    df_sorted = df.sort_values('Predicted_Submissions', ascending=True)
    df_sorted['Rank'] = range(1, len(df_sorted) + 1)
    
    # Reorder columns
    title_col = 'Problem Statement Title' if 'Problem Statement Title' in df.columns else 'Title'
    cols = ['Rank', 'Predicted_Submissions', 'Competition_Level', 'Recommendation', title_col]
    cols.extend([col for col in df_sorted.columns if col not in cols])
    df_sorted = df_sorted[cols]
    
    # Save sorted results
    output_path = excel_path.replace('.xlsx', '_SORTED_BY_COMPETITION.xlsx')
    df_sorted.to_excel(output_path, index=False)
    
    print(f"\n📊 RESULTS SUMMARY:")
    print(f"   🟢 Very Low Competition (< 30): {len(df_sorted[df_sorted['Predicted_Submissions'] < 30])}")
    print(f"   🟡 Low Competition (30-60): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 30) & (df_sorted['Predicted_Submissions'] < 60)])}")
    print(f"   🟠 Medium Competition (60-100): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 60) & (df_sorted['Predicted_Submissions'] < 100)])}")
    print(f"   🔴 High Competition (100-150): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 100) & (df_sorted['Predicted_Submissions'] < 150)])}")
    print(f"   ⚫ Very High Competition (150+): {len(df_sorted[df_sorted['Predicted_Submissions'] >= 150])}")
    
    print(f"\n💾 Results saved to: {output_path}")
    
    # Show top 5 best opportunities
    print(f"\n🏆 TOP 5 BEST OPPORTUNITIES (Lowest Competition):")
    print("=" * 70)
    
    top_5 = df_sorted.head(5)
    for idx, row in top_5.iterrows():
        title = str(row[title_col])[:50] + "..." if len(str(row[title_col])) > 50 else str(row[title_col])
        print(f"{row['Rank']:2d}. {title}")
        print(f"    Predicted: {row['Predicted_Submissions']} submissions | {row['Competition_Level']}")
        print(f"    {row['Recommendation']}")
        print()
    
    return df_sorted

if __name__ == "__main__":
    print("🚀 Starting SIH Excel Sorting Demo...")
    print("💡 This demonstrates how to sort Excel files by predicted competition level")
    print()
    
    try:
        results = demo_excel_sorting()
        print("🎉 Demo completed successfully!")
        print("📝 You can now use this system to analyze any SIH Excel file")
        print("🎯 Focus on the top-ranked opportunities for maximum success!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
