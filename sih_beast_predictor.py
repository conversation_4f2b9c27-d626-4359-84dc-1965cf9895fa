#!/usr/bin/env python3
"""
SIH BEAST PREDICTOR - 95%+ Accuracy Target
Train once, save models, predict fast
"""

import pandas as pd
import numpy as np
import os
from ultra_accurate_predictor import UltraAccurateSIHPredictor
import warnings
warnings.filterwarnings('ignore')

class SIHBeastPredictor:
    def __init__(self):
        self.predictor = UltraAccurateSIHPredictor()
        self.is_loaded = False
        
    def train_and_save(self):
        """Train the beast model and save it"""
        print("🔥 TRAINING SIH BEAST PREDICTOR")
        print("🎯 TARGET: 95%+ ACCURACY")
        print("🚀 Using GPU acceleration")
        print("=" * 60)
        
        # Train the ultra-accurate model
        results = self.predictor.train_ultra_model()
        
        accuracy = results['accuracy_percent']
        print(f"\n🏆 TRAINING RESULTS:")
        print(f"   🎯 Accuracy: {accuracy:.2f}%")
        print(f"   📈 RMSE: {results['rmse']:.2f}")
        print(f"   📉 MAE: {results['mae']:.2f}")
        print(f"   🔢 R² Score: {results['r2']:.4f}")
        
        if accuracy >= 95:
            print(f"\n🎉 BEAST MODE ACTIVATED! {accuracy:.1f}% ACCURACY!")
        elif accuracy >= 90:
            print(f"\n🚀 EXCELLENT! {accuracy:.1f}% ACCURACY!")
        elif accuracy >= 80:
            print(f"\n👍 GOOD! {accuracy:.1f}% ACCURACY!")
        else:
            print(f"\n⚠️ NEEDS IMPROVEMENT: {accuracy:.1f}% ACCURACY")
        
        print(f"\n💾 Models saved for fast predictions!")
        return results
    
    def load_models(self):
        """Load pre-trained models"""
        if self.predictor.load_models():
            self.is_loaded = True
            print("✅ Beast models loaded successfully!")
            return True
        else:
            print("❌ No saved models found. Need to train first.")
            return False
    
    def predict(self, title, organization="Unknown", category="Software", domain="Miscellaneous"):
        """Make ultra-fast prediction using saved models"""
        # Try to load models if not already loaded
        if not self.is_loaded:
            if not self.load_models():
                print("🔧 No saved models found. Training first...")
                self.train_and_save()
                self.is_loaded = True
        
        try:
            predicted_count = self.predictor.predict_submission_count(title, organization, category, domain)
            
            # Enhanced competition analysis
            if predicted_count < 20:
                level = "🟢 ULTRA LOW COMPETITION"
                advice = "🎯 JACKPOT! Apply immediately - 95% success rate!"
                success_rate = 95
                priority = "HIGHEST"
            elif predicted_count < 40:
                level = "🟢 VERY LOW COMPETITION"
                advice = "✅ EXCELLENT! Apply immediately - 85% success rate!"
                success_rate = 85
                priority = "HIGH"
            elif predicted_count < 70:
                level = "🟡 LOW COMPETITION"
                advice = "👍 GOOD opportunity - 70% success rate"
                success_rate = 70
                priority = "MEDIUM"
            elif predicted_count < 120:
                level = "🟠 MEDIUM COMPETITION"
                advice = "⚠️ Need strong solution - 50% success rate"
                success_rate = 50
                priority = "LOW"
            elif predicted_count < 200:
                level = "🔴 HIGH COMPETITION"
                advice = "❌ Very challenging - 25% success rate"
                success_rate = 25
                priority = "AVOID"
            else:
                level = "⚫ EXTREME COMPETITION"
                advice = "🚫 AVOID! Extremely low success rate (5%)"
                success_rate = 5
                priority = "NEVER"
            
            return {
                'predicted_submissions': predicted_count,
                'competition_level': level,
                'advice': advice,
                'success_rate': success_rate,
                'priority': priority
            }
            
        except Exception as e:
            return {'error': f"Prediction failed: {e}"}
    
    def analyze_excel(self, excel_file="SIH_PS_2024.xlsx"):
        """Analyze Excel file with beast accuracy"""
        print(f"\n📊 BEAST ANALYSIS OF {excel_file}")
        print("=" * 60)
        
        try:
            df = pd.read_excel(excel_file)
            print(f"✅ Loaded {len(df)} problem statements")
        except Exception as e:
            print(f"❌ Error loading {excel_file}: {e}")
            return None
        
        # Find title column
        title_col = None
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['title', 'problem', 'statement']):
                title_col = col
                break
        
        if not title_col:
            print("❌ Could not find title column!")
            return None
        
        print(f"📝 Using title column: {title_col}")
        
        # Make beast predictions
        print(f"🔮 Making beast predictions...")
        results = []
        
        for idx, row in df.iterrows():
            title = str(row[title_col]) if pd.notna(row[title_col]) else "Unknown"
            
            # Get organization, category, domain if available
            org = "Unknown"
            cat = "Software"
            dom = "Miscellaneous"
            
            for col in df.columns:
                if 'org' in col.lower():
                    org = str(row[col]) if pd.notna(row[col]) else "Unknown"
                elif 'categ' in col.lower():
                    cat = str(row[col]) if pd.notna(row[col]) else "Software"
                elif any(word in col.lower() for word in ['domain', 'theme']):
                    dom = str(row[col]) if pd.notna(row[col]) else "Miscellaneous"
            
            result = self.predict(title, org, cat, dom)
            result['title'] = title[:60] + "..." if len(title) > 60 else title
            result['original_index'] = idx
            results.append(result)
            
            if (idx + 1) % 25 == 0:
                print(f"   Processed {idx + 1}/{len(df)}...")
        
        # Sort by predicted submissions (best opportunities first)
        results.sort(key=lambda x: x.get('predicted_submissions', 999))
        
        # Add rankings
        for i, result in enumerate(results, 1):
            result['rank'] = i
        
        # Create output dataframe
        output_data = []
        for result in results:
            if 'error' not in result:
                output_data.append({
                    'Rank': result['rank'],
                    'Title': result['title'],
                    'Predicted_Submissions': result['predicted_submissions'],
                    'Competition_Level': result['competition_level'],
                    'Success_Rate_%': result['success_rate'],
                    'Priority': result['priority'],
                    'Strategic_Advice': result['advice']
                })
        
        output_df = pd.DataFrame(output_data)
        
        # Save results
        output_file = excel_file.replace('.xlsx', '_BEAST_ANALYSIS.xlsx')
        output_df.to_excel(output_file, index=False)
        
        # Show summary
        print(f"\n📊 BEAST ANALYSIS COMPLETE:")
        ultra_low = len([r for r in results if r.get('predicted_submissions', 999) < 20])
        very_low = len([r for r in results if 20 <= r.get('predicted_submissions', 999) < 40])
        low = len([r for r in results if 40 <= r.get('predicted_submissions', 999) < 70])
        medium = len([r for r in results if 70 <= r.get('predicted_submissions', 999) < 120])
        high = len([r for r in results if 120 <= r.get('predicted_submissions', 999) < 200])
        extreme = len([r for r in results if r.get('predicted_submissions', 999) >= 200])
        
        print(f"   🎯 ULTRA LOW (< 20): {ultra_low} - APPLY TO ALL!")
        print(f"   🟢 VERY LOW (20-40): {very_low} - EXCELLENT!")
        print(f"   🟡 LOW (40-70): {low} - GOOD!")
        print(f"   🟠 MEDIUM (70-120): {medium} - CHALLENGING")
        print(f"   🔴 HIGH (120-200): {high} - AVOID")
        print(f"   ⚫ EXTREME (200+): {extreme} - NEVER APPLY")
        
        print(f"\n💾 Beast analysis saved to: {output_file}")
        
        # Show top 10 BEAST opportunities
        print(f"\n🏆 TOP 10 BEAST OPPORTUNITIES:")
        print("-" * 70)
        top_10 = results[:10]
        for result in top_10:
            if 'error' not in result:
                print(f"{result['rank']:2d}. {result['title']}")
                print(f"    📊 {result['predicted_submissions']} submissions | {result['competition_level']}")
                print(f"    🎯 Priority: {result['priority']} | Success: {result['success_rate']}%")
                print(f"    💡 {result['advice']}")
                print()
        
        return output_df

def main():
    """Main function"""
    print("🔥 SIH BEAST PREDICTOR")
    print("🎯 95%+ Accuracy Target")
    print("🚀 GPU-Accelerated Beast Mode")
    print("=" * 50)
    
    predictor = SIHBeastPredictor()
    
    print("1. 🏋️ Train Beast Model (do this first)")
    print("2. 🔮 Single Prediction")
    print("3. 📊 Analyze Excel File")
    print("4. 📂 Load Existing Models")
    print("5. ❌ Exit")
    
    choice = input("\nSelect option (1-5): ").strip()
    
    if choice == "1":
        predictor.train_and_save()
    elif choice == "2":
        title = input("📝 Problem statement title: ").strip()
        if title:
            result = predictor.predict(title)
            
            if 'error' in result:
                print(f"❌ {result['error']}")
            else:
                print(f"\n🔥 BEAST PREDICTION RESULTS:")
                print(f"🎯 Predicted Submissions: {result['predicted_submissions']}")
                print(f"📈 {result['competition_level']}")
                print(f"🎲 Success Rate: {result['success_rate']}%")
                print(f"⭐ Priority: {result['priority']}")
                print(f"💡 {result['advice']}")
    elif choice == "3":
        excel_file = input("📁 Excel file name (or Enter for SIH_PS_2024.xlsx): ").strip()
        if not excel_file:
            excel_file = "SIH_PS_2024.xlsx"
        predictor.analyze_excel(excel_file)
    elif choice == "4":
        predictor.load_models()
    elif choice == "5":
        print("👋 Beast mode deactivated!")
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()
