import pandas as pd
import numpy as np
import json
import re
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud
import warnings
warnings.filterwarnings('ignore')

class SIHDataAnalyzer:
    def __init__(self):
        self.data = {}
        self.combined_data = None
        
    def load_all_data(self):
        """Load and analyze all data files"""
        print("🔍 Loading all SIH data files...")
        
        # Load CSV data
        try:
            self.data['csv'] = pd.read_csv('sih.csv')
            print(f"✅ Loaded CSV: {len(self.data['csv'])} records")
        except Exception as e:
            print(f"❌ Error loading CSV: {e}")
            
        # Load 2024 text data
        try:
            self.data['2024'] = pd.read_csv('sih2024.txt', sep='\t')
            print(f"✅ Loaded 2024 data: {len(self.data['2024'])} records")
        except Exception as e:
            print(f"❌ Error loading 2024 data: {e}")
            
        # Load Excel data
        try:
            self.data['excel'] = pd.read_excel('SIH_PS_2024.xlsx')
            print(f"✅ Loaded Excel: {len(self.data['excel'])} records")
        except Exception as e:
            print(f"❌ Error loading Excel: {e}")
            
        # Load JSON training data
        try:
            with open('training_data.json', 'r') as f:
                self.data['json'] = json.load(f)
            print(f"✅ Loaded JSON training data: {len(self.data['json'])} records")
        except Exception as e:
            print(f"❌ Error loading JSON: {e}")
            
        # Load JSONL data
        try:
            jsonl_data = []
            with open('train.jsonl', 'r') as f:
                for line in f:
                    jsonl_data.append(json.loads(line))
            self.data['jsonl'] = jsonl_data
            print(f"✅ Loaded JSONL: {len(self.data['jsonl'])} records")
        except Exception as e:
            print(f"❌ Error loading JSONL: {e}")
    
    def analyze_data_structure(self):
        """Analyze the structure of each dataset"""
        print("\n📊 ANALYZING DATA STRUCTURE")
        print("=" * 50)
        
        for name, data in self.data.items():
            if isinstance(data, pd.DataFrame):
                print(f"\n{name.upper()} Dataset:")
                print(f"Shape: {data.shape}")
                print(f"Columns: {list(data.columns)}")
                
                # Check for submission count column
                submission_cols = [col for col in data.columns if 'submit' in col.lower() or 'idea' in col.lower() or 'count' in col.lower()]
                if submission_cols:
                    print(f"Submission columns: {submission_cols}")
                    for col in submission_cols:
                        if pd.api.types.is_numeric_dtype(data[col]):
                            print(f"  {col}: min={data[col].min()}, max={data[col].max()}, mean={data[col].mean():.1f}")
                
                print(f"Sample data:")
                print(data.head(2))
                print("-" * 30)
    
    def extract_features_from_text(self, text):
        """Extract features from problem statement text"""
        if pd.isna(text) or not isinstance(text, str):
            return {
                'word_count': 0,
                'char_count': 0,
                'sentence_count': 0,
                'avg_word_length': 0,
                'has_ai_keywords': False,
                'has_blockchain_keywords': False,
                'has_health_keywords': False,
                'has_iot_keywords': False,
                'complexity_score': 0
            }
        
        # Basic text statistics
        words = text.split()
        sentences = text.split('.')
        
        # Keyword detection
        ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural network', 'cnn', 'rnn', 'nlp']
        blockchain_keywords = ['blockchain', 'cryptocurrency', 'smart contract', 'distributed ledger']
        health_keywords = ['health', 'medical', 'patient', 'healthcare', 'medtech', 'biotech']
        iot_keywords = ['iot', 'internet of things', 'sensor', 'smart device', 'connected']
        
        text_lower = text.lower()
        
        # Complexity indicators
        technical_words = ['algorithm', 'optimization', 'framework', 'architecture', 'implementation', 'integration']
        complexity_score = sum(1 for word in technical_words if word in text_lower)
        
        return {
            'word_count': len(words),
            'char_count': len(text),
            'sentence_count': len(sentences),
            'avg_word_length': np.mean([len(word) for word in words]) if words else 0,
            'has_ai_keywords': any(keyword in text_lower for keyword in ai_keywords),
            'has_blockchain_keywords': any(keyword in text_lower for keyword in blockchain_keywords),
            'has_health_keywords': any(keyword in text_lower for keyword in health_keywords),
            'has_iot_keywords': any(keyword in text_lower for keyword in iot_keywords),
            'complexity_score': complexity_score
        }
    
    def create_unified_dataset(self):
        """Create a unified dataset from all sources"""
        print("\n🔄 CREATING UNIFIED DATASET")
        print("=" * 50)
        
        unified_records = []
        
        # Process CSV data (appears to be historical data)
        if 'csv' in self.data:
            df = self.data['csv']
            for _, row in df.iterrows():
                record = {
                    'ps_number': row.get('PS Number', ''),
                    'organization': row.get('Organization', ''),
                    'title': row.get('Problem Statement Title', ''),
                    'category': row.get('Category', ''),
                    'submission_count': row.get('Submitted Idea(s) Count', 0),
                    'domain': row.get('Domain Bucket', ''),
                    'year': 2022,  # Assuming this is 2022 data based on URLs
                    'source': 'csv'
                }
                
                # Extract text features
                text_features = self.extract_features_from_text(record['title'])
                record.update(text_features)
                
                unified_records.append(record)
        
        # Process 2024 data
        if '2024' in self.data:
            df = self.data['2024']
            for _, row in df.iterrows():
                record = {
                    'ps_number': row.get('PS Number', ''),
                    'organization': row.get('Organization', ''),
                    'title': row.get('Problem Statement Title', ''),
                    'category': row.get('Category', ''),
                    'submission_count': row.get('Submitted Idea(s) Count', 0),
                    'domain': row.get('Theme', ''),
                    'year': 2024,
                    'source': '2024'
                }
                
                # Extract text features
                text_features = self.extract_features_from_text(record['title'])
                record.update(text_features)
                
                unified_records.append(record)
        
        self.combined_data = pd.DataFrame(unified_records)
        print(f"✅ Created unified dataset with {len(self.combined_data)} records")
        
        # Clean and standardize data
        self.combined_data['submission_count'] = pd.to_numeric(self.combined_data['submission_count'], errors='coerce').fillna(0)
        
        return self.combined_data
    
    def analyze_submission_patterns(self):
        """Analyze submission count patterns"""
        if self.combined_data is None:
            print("❌ No unified data available. Run create_unified_dataset() first.")
            return
        
        print("\n📈 SUBMISSION PATTERNS ANALYSIS")
        print("=" * 50)
        
        df = self.combined_data
        
        # Basic statistics
        print(f"Total records: {len(df)}")
        print(f"Submission count stats:")
        print(f"  Min: {df['submission_count'].min()}")
        print(f"  Max: {df['submission_count'].max()}")
        print(f"  Mean: {df['submission_count'].mean():.1f}")
        print(f"  Median: {df['submission_count'].median():.1f}")
        print(f"  Std: {df['submission_count'].std():.1f}")
        
        # Category analysis
        print(f"\nBy Category:")
        category_stats = df.groupby('category')['submission_count'].agg(['count', 'mean', 'std', 'min', 'max']).round(1)
        print(category_stats)
        
        # Domain analysis
        print(f"\nBy Domain/Theme:")
        domain_stats = df.groupby('domain')['submission_count'].agg(['count', 'mean', 'std', 'min', 'max']).round(1)
        print(domain_stats.head(10))
        
        # Year comparison
        if len(df['year'].unique()) > 1:
            print(f"\nBy Year:")
            year_stats = df.groupby('year')['submission_count'].agg(['count', 'mean', 'std', 'min', 'max']).round(1)
            print(year_stats)
        
        # Low competition opportunities (< 50 submissions)
        low_competition = df[df['submission_count'] < 50]
        print(f"\n🎯 LOW COMPETITION OPPORTUNITIES (< 50 submissions):")
        print(f"Found {len(low_competition)} problem statements with low competition")
        
        if len(low_competition) > 0:
            print("\nTop domains for low competition:")
            low_comp_domains = low_competition['domain'].value_counts().head(10)
            print(low_comp_domains)
    
    def save_analysis_results(self):
        """Save the unified dataset and analysis results"""
        if self.combined_data is not None:
            self.combined_data.to_csv('unified_sih_data.csv', index=False)
            print(f"\n💾 Saved unified dataset to 'unified_sih_data.csv'")
            
            # Save low competition opportunities
            low_comp = self.combined_data[self.combined_data['submission_count'] < 50]
            low_comp.to_csv('low_competition_opportunities.csv', index=False)
            print(f"💾 Saved {len(low_comp)} low competition opportunities to 'low_competition_opportunities.csv'")

if __name__ == "__main__":
    analyzer = SIHDataAnalyzer()
    analyzer.load_all_data()
    analyzer.analyze_data_structure()
    analyzer.create_unified_dataset()
    analyzer.analyze_submission_patterns()
    analyzer.save_analysis_results()
