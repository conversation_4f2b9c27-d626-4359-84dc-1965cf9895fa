import pandas as pd
import numpy as np
import re
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearReg<PERSON>, <PERSON>, Lasso
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns
from textblob import TextBlob
import warnings
warnings.filterwarnings('ignore')

class SIHMLPredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.vectorizers = {}
        self.feature_names = []
        self.best_model = None
        self.best_score = float('inf')
        
    def load_data(self, filepath='unified_sih_data.csv'):
        """Load the unified dataset"""
        self.data = pd.read_csv(filepath)
        print(f"📊 Loaded {len(self.data)} records for ML training")
        return self.data
    
    def advanced_feature_engineering(self):
        """Create sophisticated features for prediction"""
        print("🔧 Creating advanced features...")
        
        df = self.data.copy()
        
        # 1. TEXT FEATURES
        print("  📝 Extracting text features...")
        
        # Basic text statistics
        df['title_word_count'] = df['title'].fillna('').apply(lambda x: len(str(x).split()))
        df['title_char_count'] = df['title'].fillna('').apply(lambda x: len(str(x)))
        df['title_sentence_count'] = df['title'].fillna('').apply(lambda x: len(str(x).split('.')))
        df['avg_word_length'] = df['title'].fillna('').apply(
            lambda x: np.mean([len(word) for word in str(x).split()]) if str(x).split() else 0
        )
        
        # Sentiment analysis
        df['title_sentiment'] = df['title'].fillna('').apply(
            lambda x: TextBlob(str(x)).sentiment.polarity
        )
        df['title_subjectivity'] = df['title'].fillna('').apply(
            lambda x: TextBlob(str(x)).sentiment.subjectivity
        )
        
        # 2. KEYWORD FEATURES (trending technologies)
        print("  🔍 Detecting trending keywords...")
        
        keywords = {
            'ai_ml': ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural', 'cnn', 'rnn', 'nlp', 'computer vision'],
            'blockchain': ['blockchain', 'cryptocurrency', 'smart contract', 'distributed ledger', 'crypto'],
            'iot': ['iot', 'internet of things', 'sensor', 'smart device', 'connected', 'embedded'],
            'mobile': ['mobile', 'app', 'android', 'ios', 'smartphone', 'tablet'],
            'web': ['web', 'website', 'portal', 'dashboard', 'interface', 'ui', 'ux'],
            'data': ['data', 'database', 'analytics', 'visualization', 'big data', 'mining'],
            'security': ['security', 'cybersecurity', 'encryption', 'authentication', 'privacy'],
            'cloud': ['cloud', 'aws', 'azure', 'saas', 'paas', 'iaas'],
            'automation': ['automation', 'robot', 'drone', 'autonomous', 'automatic'],
            'health': ['health', 'medical', 'patient', 'healthcare', 'telemedicine', 'diagnosis']
        }
        
        for category, words in keywords.items():
            df[f'has_{category}'] = df['title'].fillna('').apply(
                lambda x: any(word in str(x).lower() for word in words)
            ).astype(int)
        
        # 3. ORGANIZATION FEATURES
        print("  🏢 Creating organization features...")
        
        # Organization type classification
        govt_keywords = ['ministry', 'department', 'government', 'isro', 'drdo', 'csir', 'iit', 'nit', 'iisc']
        private_keywords = ['ltd', 'limited', 'pvt', 'private', 'corp', 'inc', 'solutions', 'technologies']
        
        df['is_govt_org'] = df['organization'].fillna('').apply(
            lambda x: any(keyword in str(x).lower() for keyword in govt_keywords)
        ).astype(int)
        
        df['is_private_org'] = df['organization'].fillna('').apply(
            lambda x: any(keyword in str(x).lower() for keyword in private_keywords)
        ).astype(int)
        
        # Organization name length (longer names might indicate larger organizations)
        df['org_name_length'] = df['organization'].fillna('').apply(len)
        
        # 4. DOMAIN/CATEGORY FEATURES
        print("  📂 Processing domain and category features...")
        
        # Create domain popularity score based on historical data
        domain_popularity = df.groupby('domain')['submission_count'].mean().to_dict()
        df['domain_avg_submissions'] = df['domain'].map(domain_popularity).fillna(0)
        
        # Category features
        df['is_software'] = (df['category'] == 'Software').astype(int)
        df['is_hardware'] = (df['category'] == 'Hardware').astype(int)
        
        # 5. TEMPORAL FEATURES
        print("  📅 Creating temporal features...")
        
        # Year-based features
        df['is_recent_year'] = (df['year'] >= 2024).astype(int)
        df['years_since_2022'] = df['year'] - 2022
        
        # 6. COMPLEXITY INDICATORS
        print("  🧮 Calculating complexity indicators...")
        
        # Technical complexity based on keywords
        technical_words = [
            'algorithm', 'optimization', 'framework', 'architecture', 'implementation',
            'integration', 'development', 'system', 'platform', 'solution', 'model',
            'analysis', 'processing', 'recognition', 'detection', 'prediction'
        ]
        
        df['technical_complexity'] = df['title'].fillna('').apply(
            lambda x: sum(1 for word in technical_words if word in str(x).lower())
        )
        
        # Problem statement length complexity
        df['complexity_score'] = (
            df['title_word_count'] * 0.3 +
            df['technical_complexity'] * 0.7
        )
        
        # 7. INTERACTION FEATURES
        print("  🔗 Creating interaction features...")
        
        # Year-domain interaction
        df['year_domain_interaction'] = df['year'] * df['domain_avg_submissions']
        
        # Category-complexity interaction
        df['category_complexity'] = df['is_software'] * df['complexity_score']
        
        self.engineered_data = df
        print(f"✅ Created {len(df.columns) - len(self.data.columns)} new features")
        
        return df
    
    def prepare_features_for_ml(self):
        """Prepare features for machine learning"""
        print("🎯 Preparing features for ML...")
        
        df = self.engineered_data.copy()
        
        # Select features for ML
        feature_columns = [
            # Text features
            'title_word_count', 'title_char_count', 'title_sentence_count', 
            'avg_word_length', 'title_sentiment', 'title_subjectivity',
            
            # Keyword features
            'has_ai_ml', 'has_blockchain', 'has_iot', 'has_mobile', 'has_web',
            'has_data', 'has_security', 'has_cloud', 'has_automation', 'has_health',
            
            # Organization features
            'is_govt_org', 'is_private_org', 'org_name_length',
            
            # Domain/Category features
            'domain_avg_submissions', 'is_software', 'is_hardware',
            
            # Temporal features
            'is_recent_year', 'years_since_2022',
            
            # Complexity features
            'technical_complexity', 'complexity_score',
            
            # Interaction features
            'year_domain_interaction', 'category_complexity'
        ]
        
        # Handle categorical variables with encoding
        categorical_features = ['domain', 'organization']
        
        # Create TF-IDF features for problem statement titles
        print("  📊 Creating TF-IDF features...")
        tfidf = TfidfVectorizer(
            max_features=100,  # Top 100 most important words
            stop_words='english',
            ngram_range=(1, 2),  # Unigrams and bigrams
            min_df=2,  # Ignore terms that appear in less than 2 documents
            max_df=0.8  # Ignore terms that appear in more than 80% of documents
        )
        
        tfidf_features = tfidf.fit_transform(df['title'].fillna(''))
        tfidf_df = pd.DataFrame(
            tfidf_features.toarray(),
            columns=[f'tfidf_{i}' for i in range(tfidf_features.shape[1])]
        )
        
        self.vectorizers['tfidf'] = tfidf
        
        # Combine all features
        X = df[feature_columns].copy()
        
        # Handle missing values
        X = X.fillna(0)
        
        # Add TF-IDF features
        X = pd.concat([X.reset_index(drop=True), tfidf_df.reset_index(drop=True)], axis=1)
        
        # Target variable
        y = df['submission_count'].values
        
        # Store feature names
        self.feature_names = X.columns.tolist()
        
        print(f"✅ Prepared {X.shape[1]} features for {X.shape[0]} samples")
        
        return X, y
    
    def train_models(self, X, y):
        """Train multiple ML models"""
        print("🚀 Training multiple ML models...")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=None
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers['standard'] = scaler
        
        # Store test data for final evaluation
        self.X_test = X_test_scaled
        self.y_test = y_test
        
        # Define models
        models = {
            'Linear Regression': LinearRegression(),
            'Ridge Regression': Ridge(alpha=1.0),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'XGBoost': xgb.XGBRegressor(n_estimators=100, random_state=42),
            'LightGBM': lgb.LGBMRegressor(n_estimators=100, random_state=42, verbose=-1),
            'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'Neural Network': MLPRegressor(hidden_layer_sizes=(100, 50), random_state=42, max_iter=500)
        }
        
        # Train and evaluate models
        results = {}
        
        for name, model in models.items():
            print(f"  🔄 Training {name}...")
            
            # Train model
            if name == 'Neural Network':
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
            
            # Calculate metrics
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            rmse = np.sqrt(mse)
            
            results[name] = {
                'model': model,
                'mse': mse,
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'predictions': y_pred
            }
            
            # Track best model
            if rmse < self.best_score:
                self.best_score = rmse
                self.best_model = model
                self.best_model_name = name
            
            print(f"    RMSE: {rmse:.2f}, MAE: {mae:.2f}, R²: {r2:.3f}")
        
        self.models = results
        print(f"\n🏆 Best model: {self.best_model_name} (RMSE: {self.best_score:.2f})")
        
        return results

    def predict_submission_count(self, problem_statement_title, organization="Unknown",
                                category="Software", domain="Miscellaneous", year=2025):
        """Predict submission count for a new problem statement"""
        if self.best_model is None:
            raise ValueError("No trained model available. Please train the model first.")

        # Create a single row dataframe with the new problem statement
        new_data = pd.DataFrame({
            'ps_number': ['NEW_PS'],
            'organization': [organization],
            'title': [problem_statement_title],
            'category': [category],
            'submission_count': [0],  # Placeholder
            'domain': [domain],
            'year': [year],
            'source': ['prediction']
        })

        # Apply the same feature engineering
        # Text features
        new_data['title_word_count'] = new_data['title'].apply(lambda x: len(str(x).split()))
        new_data['title_char_count'] = new_data['title'].apply(lambda x: len(str(x)))
        new_data['title_sentence_count'] = new_data['title'].apply(lambda x: len(str(x).split('.')))
        new_data['avg_word_length'] = new_data['title'].apply(
            lambda x: np.mean([len(word) for word in str(x).split()]) if str(x).split() else 0
        )

        # Sentiment analysis
        new_data['title_sentiment'] = new_data['title'].apply(
            lambda x: TextBlob(str(x)).sentiment.polarity
        )
        new_data['title_subjectivity'] = new_data['title'].apply(
            lambda x: TextBlob(str(x)).sentiment.subjectivity
        )

        # Keyword features
        keywords = {
            'ai_ml': ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural', 'cnn', 'rnn', 'nlp', 'computer vision'],
            'blockchain': ['blockchain', 'cryptocurrency', 'smart contract', 'distributed ledger', 'crypto'],
            'iot': ['iot', 'internet of things', 'sensor', 'smart device', 'connected', 'embedded'],
            'mobile': ['mobile', 'app', 'android', 'ios', 'smartphone', 'tablet'],
            'web': ['web', 'website', 'portal', 'dashboard', 'interface', 'ui', 'ux'],
            'data': ['data', 'database', 'analytics', 'visualization', 'big data', 'mining'],
            'security': ['security', 'cybersecurity', 'encryption', 'authentication', 'privacy'],
            'cloud': ['cloud', 'aws', 'azure', 'saas', 'paas', 'iaas'],
            'automation': ['automation', 'robot', 'drone', 'autonomous', 'automatic'],
            'health': ['health', 'medical', 'patient', 'healthcare', 'telemedicine', 'diagnosis']
        }

        for category_name, words in keywords.items():
            new_data[f'has_{category_name}'] = new_data['title'].apply(
                lambda x: any(word in str(x).lower() for word in words)
            ).astype(int)

        # Organization features
        govt_keywords = ['ministry', 'department', 'government', 'isro', 'drdo', 'csir', 'iit', 'nit', 'iisc']
        private_keywords = ['ltd', 'limited', 'pvt', 'private', 'corp', 'inc', 'solutions', 'technologies']

        new_data['is_govt_org'] = new_data['organization'].apply(
            lambda x: any(keyword in str(x).lower() for keyword in govt_keywords)
        ).astype(int)

        new_data['is_private_org'] = new_data['organization'].apply(
            lambda x: any(keyword in str(x).lower() for keyword in private_keywords)
        ).astype(int)

        new_data['org_name_length'] = new_data['organization'].apply(len)

        # Domain features (use historical average)
        domain_popularity = self.engineered_data.groupby('domain')['submission_count'].mean().to_dict()
        new_data['domain_avg_submissions'] = new_data['domain'].map(domain_popularity).fillna(88.0)  # Overall average

        # Category features
        new_data['is_software'] = (new_data['category'] == 'Software').astype(int)
        new_data['is_hardware'] = (new_data['category'] == 'Hardware').astype(int)

        # Temporal features
        new_data['is_recent_year'] = (new_data['year'] >= 2024).astype(int)
        new_data['years_since_2022'] = new_data['year'] - 2022

        # Complexity features
        technical_words = [
            'algorithm', 'optimization', 'framework', 'architecture', 'implementation',
            'integration', 'development', 'system', 'platform', 'solution', 'model',
            'analysis', 'processing', 'recognition', 'detection', 'prediction'
        ]

        new_data['technical_complexity'] = new_data['title'].apply(
            lambda x: sum(1 for word in technical_words if word in str(x).lower())
        )

        new_data['complexity_score'] = (
            new_data['title_word_count'] * 0.3 +
            new_data['technical_complexity'] * 0.7
        )

        # Interaction features
        new_data['year_domain_interaction'] = new_data['year'] * new_data['domain_avg_submissions']
        new_data['category_complexity'] = new_data['is_software'] * new_data['complexity_score']

        # Select the same features used in training
        feature_columns = [
            'title_word_count', 'title_char_count', 'title_sentence_count',
            'avg_word_length', 'title_sentiment', 'title_subjectivity',
            'has_ai_ml', 'has_blockchain', 'has_iot', 'has_mobile', 'has_web',
            'has_data', 'has_security', 'has_cloud', 'has_automation', 'has_health',
            'is_govt_org', 'is_private_org', 'org_name_length',
            'domain_avg_submissions', 'is_software', 'is_hardware',
            'is_recent_year', 'years_since_2022',
            'technical_complexity', 'complexity_score',
            'year_domain_interaction', 'category_complexity'
        ]

        X_new = new_data[feature_columns].fillna(0)

        # Create TF-IDF features
        tfidf_features = self.vectorizers['tfidf'].transform(new_data['title'])
        tfidf_df = pd.DataFrame(
            tfidf_features.toarray(),
            columns=[f'tfidf_{i}' for i in range(tfidf_features.shape[1])]
        )

        # Combine features
        X_new = pd.concat([X_new.reset_index(drop=True), tfidf_df.reset_index(drop=True)], axis=1)

        # Scale features
        X_new_scaled = self.scalers['standard'].transform(X_new)

        # Make prediction
        if self.best_model_name == 'Neural Network':
            prediction = self.best_model.predict(X_new_scaled)[0]
        else:
            prediction = self.best_model.predict(X_new)[0]

        return max(0, int(prediction))  # Ensure non-negative integer

    def analyze_competition_level(self, predicted_count):
        """Analyze competition level based on predicted submission count"""
        if predicted_count < 30:
            return "🟢 LOW COMPETITION - High chance of selection!"
        elif predicted_count < 80:
            return "🟡 MEDIUM COMPETITION - Good chance with quality solution"
        elif predicted_count < 150:
            return "🟠 HIGH COMPETITION - Need exceptional solution"
        else:
            return "🔴 VERY HIGH COMPETITION - Extremely challenging"

    def get_strategic_recommendations(self, problem_title, predicted_count):
        """Get strategic recommendations based on prediction"""
        recommendations = []

        # Competition level advice
        if predicted_count < 50:
            recommendations.append("✅ This is a great opportunity! Low competition expected.")
            recommendations.append("💡 Focus on a solid, well-implemented solution.")
        else:
            recommendations.append("⚠️ High competition expected. Consider these strategies:")
            recommendations.append("🚀 Aim for innovative, cutting-edge approach")
            recommendations.append("🎯 Focus on unique value proposition")
            recommendations.append("📊 Include comprehensive market research")

        # Technology-specific advice
        title_lower = problem_title.lower()
        if any(word in title_lower for word in ['ai', 'ml', 'machine learning', 'artificial intelligence']):
            recommendations.append("🤖 AI/ML detected: Use latest models, show clear performance metrics")

        if any(word in title_lower for word in ['blockchain', 'crypto']):
            recommendations.append("⛓️ Blockchain detected: Focus on real-world use cases and scalability")

        if any(word in title_lower for word in ['mobile', 'app']):
            recommendations.append("📱 Mobile app detected: Prioritize UX/UI and cross-platform compatibility")

        return recommendations

if __name__ == "__main__":
    # Initialize predictor
    predictor = SIHMLPredictor()

    # Load and process data
    predictor.load_data()
    predictor.advanced_feature_engineering()
    X, y = predictor.prepare_features_for_ml()

    # Train models
    results = predictor.train_models(X, y)

    print("\n" + "="*60)
    print("🎯 SIH COMPETITION PREDICTOR - READY FOR USE!")
    print("="*60)

    # Example predictions for different types of problem statements
    test_cases = [
        {
            'title': 'AI-based healthcare diagnosis system using machine learning',
            'organization': 'AIIMS Delhi',
            'category': 'Software',
            'domain': 'MedTech / BioTech / HealthTech'
        },
        {
            'title': 'Smart agriculture monitoring using IoT sensors',
            'organization': 'Ministry of Agriculture',
            'category': 'Hardware',
            'domain': 'Agriculture, FoodTech & Rural Development'
        },
        {
            'title': 'Blockchain-based supply chain transparency platform',
            'organization': 'Tech Mahindra',
            'category': 'Software',
            'domain': 'Blockchain & Cybersecurity'
        },
        {
            'title': 'Virtual reality educational content for rural schools',
            'organization': 'Ministry of Education',
            'category': 'Software',
            'domain': 'Smart Education'
        }
    ]

    print("\n📊 SAMPLE PREDICTIONS:")
    print("-" * 60)

    for i, test_case in enumerate(test_cases, 1):
        predicted_count = predictor.predict_submission_count(
            test_case['title'],
            test_case['organization'],
            test_case['category'],
            test_case['domain']
        )

        competition_level = predictor.analyze_competition_level(predicted_count)

        print(f"\n{i}. {test_case['title'][:50]}...")
        print(f"   Organization: {test_case['organization']}")
        print(f"   Category: {test_case['category']} | Domain: {test_case['domain']}")
        print(f"   🎯 Predicted Submissions: {predicted_count}")
        print(f"   {competition_level}")

    print(f"\n🏆 Model Performance Summary:")
    print(f"   Best Model: {predictor.best_model_name}")
    print(f"   RMSE: {predictor.best_score:.2f}")
    print(f"   R² Score: {results[predictor.best_model_name]['r2']:.3f}")
    print(f"   Features Used: {len(predictor.feature_names)}")

    print(f"\n💡 How to use this predictor:")
    print(f"   1. Call predictor.predict_submission_count(your_title, org, category, domain)")
    print(f"   2. Use predictor.analyze_competition_level(predicted_count) for advice")
    print(f"   3. Get recommendations with predictor.get_strategic_recommendations(title, count)")

    print(f"\n🎯 Ready to hack the SIH system strategically! 🚀")
