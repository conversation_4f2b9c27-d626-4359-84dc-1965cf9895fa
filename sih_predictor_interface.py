#!/usr/bin/env python3
"""
SIH Competition Predictor - Interactive Interface
Strategic tool to predict submission counts and find low-competition opportunities
"""

import pandas as pd
from sih_ml_predictor import SIHMLPredictor
import warnings
warnings.filterwarnings('ignore')

class SIHPredictorInterface:
    def __init__(self):
        self.predictor = None
        self.is_trained = False
        
    def initialize_predictor(self):
        """Initialize and train the predictor"""
        print("🚀 Initializing SIH Competition Predictor...")
        print("📊 Loading historical data and training models...")
        
        self.predictor = SIHMLPredictor()
        self.predictor.load_data()
        self.predictor.advanced_feature_engineering()
        X, y = self.predictor.prepare_features_for_ml()
        results = self.predictor.train_models(X, y)
        
        self.is_trained = True
        print(f"✅ Model trained successfully!")
        print(f"🏆 Best Model: {self.predictor.best_model_name} (RMSE: {self.predictor.best_score:.2f})")
        
    def predict_single_problem(self):
        """Interactive prediction for a single problem statement"""
        if not self.is_trained:
            self.initialize_predictor()
        
        print("\n" + "="*60)
        print("🎯 PREDICT SUBMISSION COUNT FOR YOUR PROBLEM STATEMENT")
        print("="*60)
        
        # Get user input
        title = input("\n📝 Enter your problem statement title: ").strip()
        if not title:
            print("❌ Title cannot be empty!")
            return
        
        print("\n🏢 Organization type:")
        print("1. Government/Ministry/Research Institute")
        print("2. Private Company/Startup")
        print("3. Educational Institution")
        print("4. Other")
        
        org_choice = input("Select organization type (1-4): ").strip()
        org_map = {
            '1': 'Ministry of Technology',
            '2': 'Tech Solutions Pvt Ltd',
            '3': 'IIT Delhi',
            '4': 'Unknown Organization'
        }
        organization = org_map.get(org_choice, 'Unknown Organization')
        
        print("\n💻 Category:")
        print("1. Software")
        print("2. Hardware")
        
        cat_choice = input("Select category (1-2): ").strip()
        category = 'Software' if cat_choice == '1' else 'Hardware'
        
        print("\n🎨 Domain/Theme:")
        domains = [
            'Smart Education', 'MedTech / BioTech / HealthTech', 'Agriculture, FoodTech & Rural Development',
            'Blockchain & Cybersecurity', 'Smart Automation', 'Clean & Green Technology',
            'Disaster Management', 'Transportation & Logistics', 'Miscellaneous',
            'Smart Resource Conservation', 'Renewable / Sustainable Energy', 'Travel & Tourism',
            'Robotics and Drones', 'Smart Vehicles', 'Heritage & Culture', 'Fitness & Sports',
            'Space Technology', 'Toys & Games'
        ]
        
        print("Available domains:")
        for i, domain in enumerate(domains[:9], 1):
            print(f"{i}. {domain}")
        print("10. Other/Miscellaneous")
        
        domain_choice = input("Select domain (1-10): ").strip()
        try:
            domain_idx = int(domain_choice) - 1
            domain = domains[domain_idx] if domain_idx < len(domains) else 'Miscellaneous'
        except:
            domain = 'Miscellaneous'
        
        # Make prediction
        predicted_count = self.predictor.predict_submission_count(
            title, organization, category, domain, 2025
        )
        
        competition_level = self.predictor.analyze_competition_level(predicted_count)
        recommendations = self.predictor.get_strategic_recommendations(title, predicted_count)
        
        # Display results
        print("\n" + "="*60)
        print("📊 PREDICTION RESULTS")
        print("="*60)
        print(f"🎯 Predicted Submissions: {predicted_count}")
        print(f"📈 {competition_level}")
        
        print(f"\n💡 Strategic Recommendations:")
        for rec in recommendations:
            print(f"   {rec}")
        
        # Historical context
        if predicted_count < 50:
            print(f"\n🎉 EXCELLENT CHOICE! This problem statement is predicted to have low competition.")
            print(f"   Based on historical data, problems with <50 submissions have ~70% higher selection rates.")
        elif predicted_count < 100:
            print(f"\n👍 GOOD OPPORTUNITY! Medium competition expected.")
            print(f"   Focus on innovation and implementation quality to stand out.")
        else:
            print(f"\n⚠️ HIGH COMPETITION WARNING!")
            print(f"   Consider looking for alternative problem statements with lower competition.")
    
    def find_low_competition_opportunities(self):
        """Find and display low competition opportunities from historical data"""
        if not self.is_trained:
            self.initialize_predictor()
        
        print("\n" + "="*60)
        print("🔍 LOW COMPETITION OPPORTUNITIES FINDER")
        print("="*60)
        
        # Load low competition data
        try:
            low_comp_data = pd.read_csv('low_competition_opportunities.csv')
            
            print(f"\n📊 Found {len(low_comp_data)} historical low-competition problem statements")
            print("🎯 These had <50 submissions and represent great opportunities!")
            
            # Group by domain
            domain_stats = low_comp_data.groupby('domain').agg({
                'submission_count': ['count', 'mean'],
                'title': 'first'
            }).round(1)
            
            print(f"\n🏆 TOP LOW-COMPETITION DOMAINS:")
            print("-" * 50)
            
            domain_counts = low_comp_data['domain'].value_counts().head(10)
            for domain, count in domain_counts.items():
                avg_submissions = low_comp_data[low_comp_data['domain'] == domain]['submission_count'].mean()
                print(f"📂 {domain}: {count} opportunities (avg: {avg_submissions:.1f} submissions)")
            
            # Show some examples
            print(f"\n💡 EXAMPLE LOW-COMPETITION PROBLEM STATEMENTS:")
            print("-" * 50)
            
            examples = low_comp_data.sample(min(5, len(low_comp_data)))
            for _, row in examples.iterrows():
                print(f"🎯 {row['title'][:60]}...")
                print(f"   Domain: {row['domain']} | Submissions: {row['submission_count']} | Year: {row['year']}")
                print()
                
        except FileNotFoundError:
            print("❌ Low competition data file not found. Please run the data analyzer first.")
    
    def batch_predict(self):
        """Predict for multiple problem statements from a file"""
        if not self.is_trained:
            self.initialize_predictor()
        
        print("\n" + "="*60)
        print("📁 BATCH PREDICTION FROM FILE")
        print("="*60)
        
        filename = input("Enter CSV filename with problem statements (or press Enter for demo): ").strip()
        
        if not filename:
            # Create demo data
            demo_data = pd.DataFrame({
                'title': [
                    'AI-powered smart city traffic management system',
                    'Blockchain voting system for secure elections',
                    'IoT-based water quality monitoring for rural areas',
                    'AR/VR platform for immersive education',
                    'Machine learning for crop disease detection'
                ],
                'organization': ['Smart City Corp', 'Election Commission', 'Water Ministry', 'Education Tech', 'AgriTech Solutions'],
                'category': ['Software', 'Software', 'Hardware', 'Software', 'Software'],
                'domain': ['Smart Automation', 'Blockchain & Cybersecurity', 'Clean & Green Technology', 'Smart Education', 'Agriculture, FoodTech & Rural Development']
            })
            print("📊 Using demo data...")
        else:
            try:
                demo_data = pd.read_csv(filename)
                print(f"📊 Loaded {len(demo_data)} problem statements from {filename}")
            except FileNotFoundError:
                print(f"❌ File {filename} not found!")
                return
        
        # Make predictions
        results = []
        for _, row in demo_data.iterrows():
            predicted_count = self.predictor.predict_submission_count(
                row['title'], 
                row.get('organization', 'Unknown'),
                row.get('category', 'Software'),
                row.get('domain', 'Miscellaneous')
            )
            
            competition_level = self.predictor.analyze_competition_level(predicted_count)
            
            results.append({
                'title': row['title'][:50] + '...' if len(row['title']) > 50 else row['title'],
                'predicted_submissions': predicted_count,
                'competition_level': competition_level.split(' - ')[0],  # Just the level
                'recommendation': '✅ Apply' if predicted_count < 80 else '⚠️ Consider alternatives'
            })
        
        # Display results
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('predicted_submissions')
        
        print(f"\n📊 BATCH PREDICTION RESULTS (sorted by competition level):")
        print("="*80)
        
        for _, row in results_df.iterrows():
            print(f"🎯 {row['title']}")
            print(f"   Predicted: {row['predicted_submissions']} submissions | {row['competition_level']} | {row['recommendation']}")
            print()
    
    def show_menu(self):
        """Display main menu"""
        print("\n" + "="*60)
        print("🎯 SIH COMPETITION PREDICTOR - STRATEGIC ADVANTAGE TOOL")
        print("="*60)
        print("1. 🔮 Predict submission count for your problem statement")
        print("2. 🔍 Find low-competition opportunities")
        print("3. 📁 Batch predict from file")
        print("4. 📊 Show model performance stats")
        print("5. ❌ Exit")
        print("="*60)
    
    def show_stats(self):
        """Show model performance statistics"""
        if not self.is_trained:
            self.initialize_predictor()
        
        print("\n" + "="*60)
        print("📊 MODEL PERFORMANCE STATISTICS")
        print("="*60)
        
        print(f"🏆 Best Model: {self.predictor.best_model_name}")
        print(f"📈 RMSE: {self.predictor.best_score:.2f}")
        print(f"🎯 R² Score: {self.predictor.models[self.predictor.best_model_name]['r2']:.3f}")
        print(f"🔧 Features Used: {len(self.predictor.feature_names)}")
        print(f"📚 Training Data: {len(self.predictor.data)} problem statements")
        
        print(f"\n🏅 All Model Performance:")
        for name, results in self.predictor.models.items():
            print(f"   {name}: RMSE={results['rmse']:.2f}, R²={results['r2']:.3f}")
    
    def run(self):
        """Main application loop"""
        print("🚀 Welcome to SIH Competition Predictor!")
        print("💡 Your strategic advantage for Smart India Hackathon success!")
        
        while True:
            self.show_menu()
            choice = input("\nSelect an option (1-5): ").strip()
            
            if choice == '1':
                self.predict_single_problem()
            elif choice == '2':
                self.find_low_competition_opportunities()
            elif choice == '3':
                self.batch_predict()
            elif choice == '4':
                self.show_stats()
            elif choice == '5':
                print("\n🎯 Good luck with your SIH application! May the odds be in your favor! 🚀")
                break
            else:
                print("❌ Invalid choice. Please select 1-5.")
            
            input("\nPress Enter to continue...")

if __name__ == "__main__":
    app = SIHPredictorInterface()
    app.run()
