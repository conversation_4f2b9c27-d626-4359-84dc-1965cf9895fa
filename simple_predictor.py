#!/usr/bin/env python3
"""
Simple SIH Submission Predictor
Easy-to-use script for predicting submission counts with custom inputs
"""

import pandas as pd
import numpy as np
from sih_ml_predictor import SIHMLPredictor
import warnings
warnings.filterwarnings('ignore')

class SimpleSIHPredictor:
    def __init__(self):
        self.predictor = None
        self.is_trained = False
        
    def initialize(self):
        """Initialize and train the predictor"""
        print("🚀 Initializing SIH Predictor...")
        print("⏳ Training model (this may take a moment)...")
        
        self.predictor = SIHMLPredictor()
        self.predictor.load_data()
        self.predictor.advanced_feature_engineering()
        X, y = self.predictor.prepare_features_for_ml()
        results = self.predictor.train_models(X, y)
        
        self.is_trained = True
        accuracy = results[self.predictor.best_model_name]['r2'] * 100
        print(f"✅ Model trained successfully!")
        print(f"🎯 Accuracy: {accuracy:.1f}%")
        print(f"🏆 Best Model: {self.predictor.best_model_name}")
        
    def predict(self, title, organization="Unknown", category="Software", domain="Miscellaneous"):
        """Make a prediction for a problem statement"""
        if not self.is_trained:
            self.initialize()
        
        try:
            predicted_count = self.predictor.predict_submission_count(title, organization, category, domain)
            
            # Determine competition level
            if predicted_count < 30:
                level = "🟢 VERY LOW COMPETITION"
                advice = "✅ EXCELLENT CHOICE! Apply immediately - high success probability!"
                color = "green"
            elif predicted_count < 60:
                level = "🟡 LOW COMPETITION"
                advice = "👍 GOOD OPPORTUNITY! Solid chance with decent solution."
                color = "yellow"
            elif predicted_count < 100:
                level = "🟠 MEDIUM COMPETITION"
                advice = "⚠️ MODERATE COMPETITION. Need strong, innovative solution."
                color = "orange"
            elif predicted_count < 150:
                level = "🔴 HIGH COMPETITION"
                advice = "❌ HIGH COMPETITION! Consider alternatives unless exceptional."
                color = "red"
            else:
                level = "⚫ VERY HIGH COMPETITION"
                advice = "🚫 AVOID! Extremely high competition - very low success rate."
                color = "black"
            
            return {
                'predicted_submissions': predicted_count,
                'competition_level': level,
                'advice': advice,
                'color': color,
                'success_probability': self._calculate_success_probability(predicted_count)
            }
            
        except Exception as e:
            return {
                'error': f"Prediction failed: {str(e)}",
                'predicted_submissions': None,
                'competition_level': "❌ ERROR",
                'advice': "Unable to make prediction",
                'color': "red",
                'success_probability': 0
            }
    
    def _calculate_success_probability(self, predicted_count):
        """Calculate rough success probability based on competition"""
        if predicted_count < 30:
            return 85  # Very high chance
        elif predicted_count < 60:
            return 65  # Good chance
        elif predicted_count < 100:
            return 40  # Moderate chance
        elif predicted_count < 150:
            return 20  # Low chance
        else:
            return 5   # Very low chance
    
    def analyze_multiple(self, problems):
        """Analyze multiple problem statements and rank them"""
        if not self.is_trained:
            self.initialize()
        
        results = []
        
        print(f"🔮 Analyzing {len(problems)} problem statements...")
        
        for i, problem in enumerate(problems, 1):
            if isinstance(problem, str):
                # Simple string input
                result = self.predict(problem)
                result['title'] = problem[:60] + "..." if len(problem) > 60 else problem
                result['organization'] = "Unknown"
                result['category'] = "Software"
                result['domain'] = "Miscellaneous"
            elif isinstance(problem, dict):
                # Dictionary input with details
                result = self.predict(
                    problem.get('title', 'Unknown'),
                    problem.get('organization', 'Unknown'),
                    problem.get('category', 'Software'),
                    problem.get('domain', 'Miscellaneous')
                )
                result['title'] = problem.get('title', 'Unknown')
                result['organization'] = problem.get('organization', 'Unknown')
                result['category'] = problem.get('category', 'Software')
                result['domain'] = problem.get('domain', 'Miscellaneous')
            
            result['rank'] = 0  # Will be set after sorting
            results.append(result)
            
            print(f"   {i}/{len(problems)} completed...")
        
        # Sort by predicted submissions (low to high = better opportunities)
        results.sort(key=lambda x: x.get('predicted_submissions', 999))
        
        # Add rankings
        for i, result in enumerate(results, 1):
            result['rank'] = i
        
        return results
    
    def display_results(self, results):
        """Display analysis results in a nice format"""
        print("\n" + "="*80)
        print("🏆 SIH PROBLEM STATEMENT ANALYSIS RESULTS")
        print("="*80)
        
        print(f"\n📊 SUMMARY:")
        very_low = len([r for r in results if r.get('predicted_submissions', 999) < 30])
        low = len([r for r in results if 30 <= r.get('predicted_submissions', 999) < 60])
        medium = len([r for r in results if 60 <= r.get('predicted_submissions', 999) < 100])
        high = len([r for r in results if 100 <= r.get('predicted_submissions', 999) < 150])
        very_high = len([r for r in results if r.get('predicted_submissions', 999) >= 150])
        
        print(f"   🟢 Very Low Competition (< 30): {very_low}")
        print(f"   🟡 Low Competition (30-60): {low}")
        print(f"   🟠 Medium Competition (60-100): {medium}")
        print(f"   🔴 High Competition (100-150): {high}")
        print(f"   ⚫ Very High Competition (150+): {very_high}")
        
        print(f"\n🎯 TOP OPPORTUNITIES (Best to Worst):")
        print("-" * 80)
        
        for result in results[:10]:  # Show top 10
            if 'error' in result:
                print(f"{result['rank']:2d}. ❌ ERROR: {result['error']}")
                continue
                
            print(f"{result['rank']:2d}. {result['title']}")
            print(f"    📊 Predicted: {result['predicted_submissions']} submissions")
            print(f"    🎯 {result['competition_level']}")
            print(f"    📈 Success Probability: {result['success_probability']}%")
            print(f"    💡 {result['advice']}")
            print()

def interactive_mode():
    """Interactive mode for single predictions"""
    predictor = SimpleSIHPredictor()
    
    print("🎯 SIH SUBMISSION PREDICTOR - INTERACTIVE MODE")
    print("=" * 60)
    print("💡 Enter your problem statement details for prediction")
    print("📝 Press Ctrl+C to exit anytime")
    print()
    
    try:
        while True:
            print("-" * 60)
            title = input("📝 Problem Statement Title: ").strip()
            if not title:
                print("❌ Title cannot be empty!")
                continue
            
            organization = input("🏢 Organization (optional): ").strip() or "Unknown"
            
            print("\n💻 Category:")
            print("1. Software")
            print("2. Hardware")
            cat_choice = input("Select (1-2, default=1): ").strip()
            category = "Hardware" if cat_choice == "2" else "Software"
            
            print("\n🎨 Domain (optional, press Enter for auto-detect): ")
            domain = input("Domain: ").strip() or "Miscellaneous"
            
            print("\n🔮 Making prediction...")
            result = predictor.predict(title, organization, category, domain)
            
            print("\n" + "="*60)
            print("📊 PREDICTION RESULTS")
            print("="*60)
            
            if 'error' in result:
                print(f"❌ {result['error']}")
            else:
                print(f"🎯 Predicted Submissions: {result['predicted_submissions']}")
                print(f"📈 {result['competition_level']}")
                print(f"🎲 Success Probability: {result['success_probability']}%")
                print(f"💡 {result['advice']}")
            
            print("\n" + "="*60)
            
            again = input("\n🔄 Analyze another problem? (y/n): ").strip().lower()
            if again not in ['y', 'yes']:
                break
                
    except KeyboardInterrupt:
        print("\n\n👋 Thanks for using SIH Predictor!")
    except Exception as e:
        print(f"\n❌ Error: {e}")

def batch_mode():
    """Batch mode for multiple predictions"""
    predictor = SimpleSIHPredictor()
    
    print("📁 SIH SUBMISSION PREDICTOR - BATCH MODE")
    print("=" * 60)
    
    # Example problems for demo
    demo_problems = [
        {
            'title': 'AI-powered healthcare diagnosis system using machine learning',
            'organization': 'AIIMS Delhi',
            'category': 'Software',
            'domain': 'MedTech / BioTech / HealthTech'
        },
        {
            'title': 'Blockchain-based supply chain management platform',
            'organization': 'Tech Mahindra',
            'category': 'Software',
            'domain': 'Blockchain & Cybersecurity'
        },
        {
            'title': 'IoT sensor network for smart agriculture monitoring',
            'organization': 'Ministry of Agriculture',
            'category': 'Hardware',
            'domain': 'Agriculture, FoodTech & Rural Development'
        },
        {
            'title': 'Virtual reality educational platform for rural schools',
            'organization': 'Ministry of Education',
            'category': 'Software',
            'domain': 'Smart Education'
        },
        {
            'title': 'Smart traffic management system using computer vision',
            'organization': 'Smart City Corp',
            'category': 'Software',
            'domain': 'Smart Automation'
        }
    ]
    
    print("🔮 Running batch analysis on demo problems...")
    results = predictor.analyze_multiple(demo_problems)
    predictor.display_results(results)
    
    print(f"\n💡 To analyze your own problems, modify the 'demo_problems' list in the script!")

if __name__ == "__main__":
    import sys
    
    print("🎯 SIH SUBMISSION PREDICTOR")
    print("=" * 50)
    print("Choose mode:")
    print("1. 🔮 Interactive Mode (one-by-one predictions)")
    print("2. 📁 Batch Mode (analyze multiple problems)")
    print("3. ❌ Exit")
    
    try:
        choice = input("\nSelect mode (1-3): ").strip()
        
        if choice == "1":
            interactive_mode()
        elif choice == "2":
            batch_mode()
        elif choice == "3":
            print("👋 Goodbye!")
        else:
            print("❌ Invalid choice!")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
