#!/usr/bin/env python3
"""
SIH Excel Competition Sorter
Sorts SIH_PS_2024.xlsx by predicted competition level (low to high)
"""

import pandas as pd
import numpy as np
from sih_ml_predictor import SIHMLPredictor
import warnings
warnings.filterwarnings('ignore')

def sort_sih_excel():
    """Sort SIH Excel file by predicted competition level"""
    print("🎯 SIH EXCEL COMPETITION SORTER")
    print("=" * 50)
    
    # Initialize predictor
    print("🚀 Initializing ML predictor...")
    predictor = SIHMLPredictor()
    predictor.load_data()
    predictor.advanced_feature_engineering()
    X, y = predictor.prepare_features_for_ml()
    results = predictor.train_models(X, y)
    
    accuracy = results[predictor.best_model_name]['r2'] * 100
    print(f"✅ Model ready! Accuracy: {accuracy:.1f}% | Best Model: {predictor.best_model_name}")
    
    # Load Excel file
    excel_file = "SIH_PS_2024.xlsx"
    try:
        print(f"\n📁 Loading {excel_file}...")
        df = pd.read_excel(excel_file)
        print(f"✅ Loaded {len(df)} problem statements")
    except FileNotFoundError:
        print(f"❌ File not found: {excel_file}")
        print("💡 Make sure the file is in the current directory")
        return None
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return None
    
    # Display column names to understand structure
    print(f"\n📊 Excel columns found:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i}. {col}")
    
    # Auto-detect columns (flexible approach)
    title_col = None
    org_col = None
    category_col = None
    domain_col = None
    
    # Find title column
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['title', 'problem', 'statement']):
            title_col = col
            break
    
    # Find organization column
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['organization', 'org', 'ministry', 'department']):
            org_col = col
            break
    
    # Find category column
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['category', 'type']):
            category_col = col
            break
    
    # Find domain/theme column
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['domain', 'theme', 'area', 'bucket']):
            domain_col = col
            break
    
    print(f"\n🔍 Auto-detected columns:")
    print(f"  Title: {title_col or 'Not found'}")
    print(f"  Organization: {org_col or 'Not found'}")
    print(f"  Category: {category_col or 'Not found'}")
    print(f"  Domain: {domain_col or 'Not found'}")
    
    if not title_col:
        print("❌ Could not find title column! Please check the Excel file structure.")
        return None
    
    # Make predictions
    print(f"\n🔮 Making predictions for all {len(df)} problem statements...")
    predictions = []
    competition_levels = []
    recommendations = []
    success_probabilities = []
    
    for idx, row in df.iterrows():
        try:
            # Extract data with fallbacks
            title = str(row[title_col]) if pd.notna(row[title_col]) else "Unknown Title"
            organization = str(row[org_col]) if org_col and pd.notna(row[org_col]) else "Unknown"
            category = str(row[category_col]) if category_col and pd.notna(row[category_col]) else "Software"
            domain = str(row[domain_col]) if domain_col and pd.notna(row[domain_col]) else "Miscellaneous"
            
            # Make prediction
            pred_count = predictor.predict_submission_count(title, organization, category, domain)
            predictions.append(pred_count)
            
            # Determine competition level and recommendations
            if pred_count < 30:
                level = "🟢 VERY LOW"
                recommendation = "✅ EXCELLENT CHOICE! Apply immediately - high success rate!"
                success_prob = 85
            elif pred_count < 60:
                level = "🟡 LOW"
                recommendation = "👍 GOOD OPPORTUNITY! Solid chance with decent solution."
                success_prob = 65
            elif pred_count < 100:
                level = "🟠 MEDIUM"
                recommendation = "⚠️ MODERATE COMPETITION. Need strong, innovative solution."
                success_prob = 40
            elif pred_count < 150:
                level = "🔴 HIGH"
                recommendation = "❌ HIGH COMPETITION! Consider alternatives unless exceptional."
                success_prob = 20
            else:
                level = "⚫ VERY HIGH"
                recommendation = "🚫 AVOID! Extremely high competition - very low success rate."
                success_prob = 5
            
            competition_levels.append(level)
            recommendations.append(recommendation)
            success_probabilities.append(success_prob)
            
            # Progress indicator
            if (idx + 1) % 50 == 0:
                print(f"   Processed {idx + 1}/{len(df)} problems...")
                
        except Exception as e:
            print(f"⚠️ Error predicting for row {idx + 1}: {e}")
            predictions.append(999)  # High number to sort to bottom
            competition_levels.append("❌ ERROR")
            recommendations.append("Error in prediction")
            success_probabilities.append(0)
    
    # Add predictions to dataframe
    df['Predicted_Submissions'] = predictions
    df['Competition_Level'] = competition_levels
    df['Success_Probability_%'] = success_probabilities
    df['Strategic_Recommendation'] = recommendations
    
    # Sort by predicted submissions (low to high = better opportunities)
    df_sorted = df.sort_values('Predicted_Submissions', ascending=True)
    
    # Add ranking
    df_sorted['Opportunity_Rank'] = range(1, len(df_sorted) + 1)
    
    # Reorder columns to put important info first
    important_cols = ['Opportunity_Rank', 'Predicted_Submissions', 'Competition_Level', 
                     'Success_Probability_%', 'Strategic_Recommendation']
    
    # Add the title column right after ranking info
    if title_col:
        important_cols.append(title_col)
    
    # Add remaining columns
    remaining_cols = [col for col in df_sorted.columns if col not in important_cols]
    final_cols = important_cols + remaining_cols
    
    df_sorted = df_sorted[final_cols]
    
    # Display summary statistics
    print(f"\n📊 COMPETITION ANALYSIS SUMMARY:")
    print(f"   🟢 Very Low Competition (< 30): {len(df_sorted[df_sorted['Predicted_Submissions'] < 30])}")
    print(f"   🟡 Low Competition (30-60): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 30) & (df_sorted['Predicted_Submissions'] < 60)])}")
    print(f"   🟠 Medium Competition (60-100): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 60) & (df_sorted['Predicted_Submissions'] < 100)])}")
    print(f"   🔴 High Competition (100-150): {len(df_sorted[(df_sorted['Predicted_Submissions'] >= 100) & (df_sorted['Predicted_Submissions'] < 150)])}")
    print(f"   ⚫ Very High Competition (150+): {len(df_sorted[df_sorted['Predicted_Submissions'] >= 150])}")
    
    # Save sorted results
    output_file = "SIH_PS_2024_SORTED_BY_COMPETITION.xlsx"
    try:
        df_sorted.to_excel(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
    except Exception as e:
        print(f"❌ Error saving file: {e}")
        return df_sorted
    
    # Show top 10 best opportunities
    print(f"\n🏆 TOP 10 BEST OPPORTUNITIES (Lowest Competition):")
    print("=" * 80)
    
    top_10 = df_sorted.head(10)
    for _, row in top_10.iterrows():
        title = str(row[title_col])[:60] + "..." if len(str(row[title_col])) > 60 else str(row[title_col])
        print(f"{row['Opportunity_Rank']:2d}. {title}")
        print(f"    📊 Predicted: {row['Predicted_Submissions']} submissions")
        print(f"    🎯 {row['Competition_Level']} | Success Rate: {row['Success_Probability_%']}%")
        print(f"    💡 {row['Strategic_Recommendation']}")
        print()
    
    # Show bottom 5 (highest competition - avoid these)
    print(f"\n⚠️ TOP 5 HIGHEST COMPETITION (AVOID THESE):")
    print("=" * 80)
    
    bottom_5 = df_sorted.tail(5)
    for _, row in bottom_5.iterrows():
        title = str(row[title_col])[:60] + "..." if len(str(row[title_col])) > 60 else str(row[title_col])
        print(f"{row['Opportunity_Rank']:2d}. {title}")
        print(f"    📊 Predicted: {row['Predicted_Submissions']} submissions")
        print(f"    🎯 {row['Competition_Level']} | Success Rate: {row['Success_Probability_%']}%")
        print()
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print(f"📁 Sorted Excel file saved as: {output_file}")
    print(f"🎯 Focus on the top-ranked opportunities for maximum success!")
    print(f"💡 Apply to 3-5 problems from the top 20 for best results!")
    
    return df_sorted

if __name__ == "__main__":
    try:
        result = sort_sih_excel()
        if result is not None:
            print(f"\n🚀 Ready to strategically dominate SIH 2025!")
        else:
            print(f"\n❌ Analysis failed. Please check the Excel file and try again.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
