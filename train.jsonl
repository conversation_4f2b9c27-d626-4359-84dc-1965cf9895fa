{"id": "SIH1525", "title": "Development of software application for analysis and processing of dvbs2 receiver output stream i.e., raw BB Frames, GSE and TS in near real time.", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Development of software application for analysis and processing of dvbs2 receiver output stream i.e., raw BB Frames, GSE and TS in near real time.. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: 1. Classification  identification of audio, video, data  protocol such as MPE, ULE, SIP, RTP, FTP, SFTP, HTTP, HTTPS, SNMP, POP, SMTP, SSH etc. 2. Classification Identification of encryptionscrambling if present in stream through headers and SI tables 3. Extraction of VoIP calls, audio and video programs, file, email, webpage etc in separate files. 4. Decoding and playing selected audiovideo contents Preferred Language Python, Labview, CC, VHDLVerilog Expected Outcome AlgorithmSoftwareGUI Preferred Platform WindowsLinux"}
{"id": "SIH1524", "title": "Domain Name Server DNS Filtering Service using Threat Intelligence feeds and AIML Techniques", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Domain Name Server DNS Filtering Service using Threat Intelligence feeds and AIML Techniques. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: DNS Filtering service helps block malicious domains and prevent malware from communicating with Commandandcontrol servers. It also aids in blocking phishing attacks, playing a crucial role in enhancing security and ensuring appropriate content access. The solution should provide a secure DNS resolver that blocks resolution of malicious domain names. It should be capable of detecting and filtering malicious domains using blacklists, threat intelligence feeds with support for STIXTAXII protocol, and leveraging ALML for identifying malicious domains generated using domain generation algorithms or any other techniques employed by botnets. The solution should also be able to detect attempts at DNS tunnelling employed by malwares. It should allow the resolution of nonmalicious domain names while blocking the resolution of malicious domains. Furthermore, the solution should support DNS over UDP, DNS over DTLS and DNS over HTTPS for DNS resolution. It should be scalable to handle a large volume of DNS queries and maintain an average DNS lookup time within 100 milliseconds. Additionally, the solution should support DNS caching for faster resolution. The solution should be capable of conducting both active analysis of DNS queries to filter malicious domains and passive analysis of DNS data provided in PCAP format or Zeek TSV format for the detection of malicious domains. It should also provide a web interface for monitoring statistics on malicious domains identified in DNS queries, as well as source IP addresses that generated those queries."}
{"id": "SIH1523", "title": "Quantum Secure Email Client Application", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Quantum Secure Email Client Application. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: Email communication is a fundamental part of modern business and personal interactions. However, conventional encryption mechanisms are susceptible to various threats, including eavesdropping, maninthemiddle attacks, and data breaches in the post quantum era. Quantum Key Distribution QKD offers an unprecedented level of security by distributing secure keys to two users over a quantum channel and these keys can be used for encryption to achieve unconditionalenhanced security. The objective is to develop an email client application like Microsoft Outlook can be named as QuMail which integrates Quantum Key Distribution QKD service with existing email protocols to enhance the security of email communication while maintaining compatibility with widely used email servers Gmail, yahoo mail etc. This application interfaces with KM Key Manager for getting the quantum keys using APIs illustrated below, with Email servers through standard protocols to utilize email service facility and with users through GUI for capturing various user inputs. Use case Scenario Users having quantum keys access through the KM need to exchange email with attachment over untrusted networks i.e. internet and existing email service providers Gmail, Yahoo mail etc. It is assumed that local key managers at both ends have already generated symmetrical quantum keys at their ends. The participants may start with a key bank of 100 keys, each of size 1Kb at KMs. Features Implemented solutions shall have good modularity to allow easy upgradation KM service and email service login feature Three level of security configuration a. Level 1 No Quantum security b. Level 2 Quantumaided AES use Quantum keys as seed for AES c. Level 3 Quantum Secure  use One Time Pad d. Any other encryption may be given as option Challenges Develop a robust mechanism to seamlessly integrate QKD technology into existing email infrastructure. This includes using a secure key which is distributed using QKD, in existing email communication for encryption while maintaining interoperability. An approach to this problem can be Encryption at application layer can be introduced in email client which will use QKD key. Preferred OS Windows Note The participants can make further assumptions regarding the problem. The features listed here are baseline and do not limit the scope of application development."}
{"id": "SIH1522", "title": "Development of UT user terminal to UT Voice, Image, Video and Data Communication APP Android or iOS using Bluetooth 2.0 or above wireless interface and custom protocol", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Development of UT user terminal to UT Voice, Image, Video and Data Communication APP Android or iOS using Bluetooth 2.0 or above wireless interface and custom protocol. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: Twoway voice, video and data Communication between two UTs through BLE Bluetooth needs to be addressed using Custom Protocol with minimum signalling overheads. The APP should establish a BLE connection among peer UT as part of initialization and thereafter carry out fullduplex communication using a custom protocol format viz Header Payload. The size of header should not be more than 18th of Payload size and the delaylatency in voice or data communication should be less than 200ms. The header should contain a Unique word with advanced features so as to facilitate synchronization with minimal false detection probability. It is expected that the application uses a standard ITU vocoder and maintains a constant transfer rate between UTs. This is an essential feature for the end application. UI The UI should have options for voice, Image, Video or data transfer. It is also expected to have a builtin dialer, contact list wherein each UT is assigned a unique IDContact ID through which communication could be established. Note The custom protocol and sample data will be made available after proposal evaluation."}
{"id": "SIH1521", "title": "Development of Explainable AI XAI based model for prediction of heavy high impact rain events using satellite data", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Development of Explainable AI XAI based model for prediction of heavy high impact rain events using satellite data. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: Nowcasting of heavy precipitation rainfall events with an understanding of the most important predictors and also an idea as to why a certain model can fail. Desired Outcome The developed system shall provide the following 1. AI based model to predict particular rain episodes of greater impact using satellite data INSAT3D3DR . 2. An explainable module into the AI model XAI 3. The final output should be in terms of a web application, with associated accuracy of the models worked on and an explainable component of the outputs."}
{"id": "SIH1520", "title": "Development of NTRIP Network Transport of RTCM via internet protocol Caster, NTRIP Client and Server on web or mobile platform.", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Development of NTRIP Network Transport of RTCM via internet protocol Caster, NTRIP Client and Server on web or mobile platform.. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: Server development for RTK correction reception from NTRIP Server and Transmission to NTRIP client. Desired Outcome The developed system shall provide the following 1. Web or mobile application for transmitting corrections to NTRIP caster and receiving the correction from NTRIP caster 2. User interface for real time display with map support. 3. Time tagged Data logging."}
{"id": "SIH1519", "title": "Generation of Hazard map at 1m grid spacing 1m height resolution using 5m spatial resolution data for safely navigating a Lander to a safe landing site using Super resolution techniques.", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Generation of Hazard map at 1m grid spacing 1m height resolution using 5m spatial resolution data for safely navigating a Lander to a safe landing site using Super resolution techniques.. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: We have TMC images of nearly 80 area of the moon at 5m resolution while OHRC data at 25cm resolution have very limited coverage. Due to limited coverage of OHRC, there are constraints to land at any place on the moons surface. Hence, a problem has been defined to create hazard map using super resolution techniques from TMC 5m images considering the hazard definitions like Slope 10 degree, Crater  Boulder depthheight  1m, Crater distribution, shadow etc. for safely navigating a Lander. This challenge includes the showcasing of lander navigation techniques for safe landing considering reference as TMC 5m datasets in near real time."}
{"id": "SIH1518", "title": "Change detection due to human activities.", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Change detection due to human activities.. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: Using satellite imagery, create an automated system for detecting change related only to human activities from satellite imagery. i.e. Develop AIML based model for change detection of only manmade objects like vehicles, buildings, roads, aircraft etc. from remote sensing images Data Sentinel2, LISS4"}
{"id": "SIH1517", "title": "Identification of place names from a natural language sentence.", "category": "Space Technology", "subcategory": "Software", "organization": "Indian Space Research Organisation ISRO", "text": "Title: Identification of place names from a natural language sentence.. Category: Space Technology. Subcategory: Software. Organization: Indian Space Research Organisation ISRO. Description: Building a geospatial querying system based on natural language requires identifying geospatial entities by their names. The scope of this work involves automatically identifying names of places from a sentence. A universe of canonical names single way of spelling and correct spelling will be provided in the form of tables. For example, a Country, City and State table containing the possible universe of names. It should take into account spelling errors in the names and multiple ways of spelling and mentioning the same entity in the query and map it to a canonical name. For example Given an input Which of the following saw the highest average temperature in January, Maharashtra, Ahmedabad or entire NewZealand Here, first the system should use NLP to filter out words that are not names of places, then it must do a fuzzy match to find candidate canonical names in tables and report the output. Output Token Maharashtra, Canonical name Maharashtra, table State Token Ahmedabad, Canonical name ahmedabad, table City Token NewZealand, Canonical name new zealand, table Country Please note that the query need not be a question. It can also be an imperative sentence for example. Show me a graph of rainfall for Chennai for the month of October"}
{"id": "SIH1516", "title": "Suggest an Albased solution to enable ease of grievance lodging and tracking for citizens across multiple departments", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Housing and Urban Affairs", "text": "Title: Suggest an Albased solution to enable ease of grievance lodging and tracking for citizens across multiple departments. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Housing and Urban Affairs. Description: A robust grievance redressal mechanism is a crucial component of any administration. An efficient and effective procedure for addresstng grievances demonstrates an administrations accountability responsiveness, and userfriendliness. However, the ease of lodging a complaint or grievance by citizens is often lacking in many lndian cities. Given the large migratory population in tndian cities, consisting of individuals who may not be familiar with English, Hindi, or the local regional language, citizens face challenges in lodging their grievances. Moreover, the process of lodging a gflevance is not always straightforward. Some department websites are inaccessible, and locating the correct website for a specific department can be difficult. lntroducing an Albased chatbot that allows citizens to dictate their grievances in their local language and lodge them, would greatly assist citizens. This tool should be able to understand and process complaints effectively, assign them to the relevant department, and provide citizens with a unique complaint number. Realtime updates on the status of the complaint should be sent to citizens, enabling oneonone conversations throughout the grievance lifecycle. The primary objective of this solution should be to provide citizens with an easyto_use chatbot that facilitates efficient lodging and tracking of grievances. This would not only save citizens time in searching for the appropriate department or category but also enabl the administration to receive targeted grievances and enhance overall service delivery."}
{"id": "SIH1515", "title": "Smart and Effective realtime Management of street parking", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Housing and Urban Affairs", "text": "Title: Smart and Effective realtime Management of street parking. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Housing and Urban Affairs. Description: ln lndia, the number of vehicles is constanfly on the rise, while the availability of parking space in cities has not kept pace with the increasing vehicle numbers, primarily due to the rapid groMh in population. Consequenfly, issues such as traffic congestion, narrowed streets due to onstreet parking, a mismatch between parking supply and demand, and illegal parking have become all too familiar in lndian cities. Parking space has become a scarce commodity, and instead of simply increasing the number of available parking spaces, it is crucial to employ effective technologybased solutions to optimize their utilization. Smart parking solutions, utilizing sensors and software, can provade realtime information on available parking spaces to both city officials and drivers. Furthermore, leveraging emerging technologies can help optimize parking prices. A low parking price may encourage more vehicles on the road, leading to increased air and noise pollution, whereas too high prices may generate less revenue and less efectuve resource usage.Apart from the loss of revenue for operators, there is economic downside to the business in vicinity, effect on government taxes, employment.At the same parking space area and costs also have a corelation to balance optimum usage and revenue. Therefore, setting the right price for parking based on demand and optimizing occupancy is the best approach. The primary objective of the solution should be to equip city administrators with an effective parking management tool that can predict, manage, and finance parking in cities. An app should be developed to allow citizens to conveniently reserve pce king spots and make payments based on dynamic pricing This not only saves citizens time spent searching the parking but also reduces environmental degradation resulting from congestion caused by parking and provides a sustainable source of revenue for the city administration. We need an innovative, simple and widely coveraged parking needs in the city and also become most compliant for Traffic and mobility needs."}
{"id": "SIH1514", "title": "Comprehensive lnspection and Analysis of Water Supply Distribution Lines", "category": "Clean  Green Technology", "subcategory": "Software", "organization": "Ministry of Housing and Urban Affairs", "text": "Title: Comprehensive lnspection and Analysis of Water Supply Distribution Lines. Category: Clean  Green Technology. Subcategory: Software. Organization: Ministry of Housing and Urban Affairs. Description: Water resources across the globe are slowly on the depletion and its forecasted that the ZERO water day will not be far away if water resources ate not protected well. one the the crucial area that stilp lies in the human hands is to save the watet leakages and pay a strong water distrulibution network that improves effeicient use of water.The challenge is to conduct thorough inspections of water supply distribution lines, ranging from 100 to 200mm in diameter, in order to detect leakages, pilferage, damage, and other issues. The solution requires access to a cloud_based dashboard for data analytics, visualization, and report generation. The solution enhance service delivery, improve repair work efficiency, reduce water leakage, and streamline pipeline condition assessment. The solution should enable immediate action on contamination complaints, early detection of leaks, reduction in labor_intensive breakdown management, and facilitate GIS mapping of the pipelines. Overcoming deployment constraints involving live inspections, image analytics, and laser projections will ensure accurate and efficient assessment of the water supply distribution lines."}
{"id": "SIH1513", "title": "Effective management of construction and demolition CD waste", "category": "Clean  Green Technology", "subcategory": "Hardware", "organization": "Ministry of Housing and Urban Affairs", "text": "Title: Effective management of construction and demolition CD waste. Category: Clean  Green Technology. Subcategory: Hardware. Organization: Ministry of Housing and Urban Affairs. Description: The problem at hand revolves around the management of construction and demolition CD waste in urban areas. With rapid urbanization and infrastructure development, the volume of CD waste generated has reached alarming levels. The current practices for CD waste disposal often lead to environmental degradation, resource depletion, and public health hazards. Therefore, there is a pressing need for a comprehensive and sustainable approach to CD waste management that focuses on reducing waste generation, promoting recycling and reuse, and ensuring proper disposal methods. This will contribute to sustainable urban development by minimizing environmental impact, conserving resources, and creating a healthier living environment for communities. The problem solution should be addressed at the Hardware level and also forefronted using an intelligent monitoring system"}
{"id": "SIH1512", "title": "Centralized Monitoring System for Street Light Fault Detection and Location Tracking", "category": "Smart Automation", "subcategory": "Hardware", "organization": "Ministry of Housing and Urban Affairs", "text": "Title: Centralized Monitoring System for Street Light Fault Detection and Location Tracking. Category: Smart Automation. Subcategory: Hardware. Organization: Ministry of Housing and Urban Affairs. Description: Electricity is the critical need for progress of the livelihood.ln many Indian cities, the maintenance of street lights has become a challenging and inefficient process due to the lack of a centralized monitoring system. ldentifying faults, such as non functioning lights, current leakage and cable breakage, relies on citizen grievances, leading to delays, increased costs, and safety concerns. Linemen spend valuable time manually searching for faults, diagnosing issues, and fixing them, which can take several days to complete. The absence of precise fault location information further complicates the process. To overcome these obstacles, we seek an innovative solution that provides realtime fault detection, accurate identification of fault types, and precise location tracking of faulty street lights. This solution aims to empower linemen with efficient fault management capabilities, reducing their workload and ensuring timely maintenance. Moreover, it should enable the local authorities to proactively address faults, enhance service quality, and optimize street light maintenance processes in their respective cities..The prime aim of this problem statement is to develop a Automated Defect Detection and Prevention Assistance with Effective Governance for Cities in India"}
{"id": "SIH1511", "title": "Real time Knowledge of ore body being mined out", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Mines", "text": "Title: Real time Knowledge of ore body being mined out. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Mines. Description: The orebody model, containing blockwise or locationwise ore quality details, is prepared in the mine planning software and this information can be documented for further use through maps or spreadsheets. But from the operator\u00e2s perspective, the ore quality information for ensuring quality control during excavation is done purely through eye judgment and previous experiences. Solution Desired Through use of high precision GPS sensors, location including the elevation data of the shovelsloaders can be traced and by interlacing this information in the ore block model data, we can convey the information to the operator suitably so that quality control can be executed at the site itself."}
{"id": "SIH1510", "title": "Land profiling of Asset Creation process", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Rural Development", "text": "Title: Land profiling of Asset Creation process. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Rural Development. Description: There are various types of permissible works being taken up under the Mahatma Gandhi NREGS which are earthwork related works which requires excavationland cuttin land filling land levelling etc. As per the current practice of execution of the works under the scheme, there is a photograph taken of the worksite before initiating the work and the estimated quantity of the work to be executed in form of technical design  estimate. During the stages of execution, the technical personengineer incharge of the site, maintains the work done on site in form of measurements in the measurement book and accordingly the payment of the labour  material is generated. lt becomes difficult to ascertain the exact work done on ground during the auditinspections after the work completion. A land profile of the worksite in terms of contour levels or 3D modelling before initiating the work and after work completion can provide a necessary information to derive the actual work done through analysing the land profiles in 3 dimensions 3D1. The profiles can be generated in reference to a fixed benchmark level available on the worksite. ln this regard a technology driven solution for 3D Profiling may be provided."}
{"id": "SIH1508", "title": "Creation of dedicated real time Procurement Data Portalas well as Vendor Payment Data portalto capture all related data in respect of NEEPCO,S Procurements and Payments including Procurement from MSE Sector and procurement in GeM Portal", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Power", "text": "Title: Creation of dedicated real time Procurement Data Portalas well as Vendor Payment Data portalto capture all related data in respect of NEEPCO,S Procurements and Payments including Procurement from MSE Sector and procurement in GeM Portal. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Power. Description: This challenge involves developing a comprehensive online portal for North Eastern Electric Power Corporation NEEPCO to capture and manage all data related to their procurement processes and vendor payments. The portal must have the capability to record procurement activities, particularly those involving Micro and Small Enterprises MSE and purchases made through the Government eMarketplace GeM portal. The solution should enable efficient data entry, storage, retrieval, and analysis of procurement details, payment records, and vendor information. It should also ensure data security and compliance with relevant regulations. By addressing this challenge, the aim is to enhance transparency, streamline procurement operations, promote MSE participation, and facilitate effective financial management for NEEPCO."}
{"id": "SIH1507", "title": "Frequent dislodgement of belt conveyor along hilly terrain for various reasons", "category": "Transportation  Logistics", "subcategory": "Hardware", "organization": "Ministry of Mines", "text": "Title: Frequent dislodgement of belt conveyor along hilly terrain for various reasons. Category: Transportation  Logistics. Subcategory: Hardware. Organization: Ministry of Mines. Description: The 14.6 km long cable belt conveyor passing over complex hilly terrain suffers belt dislodgements due to various reasons resulting in the sudden loss of production. Solution Desired Capture reasons for belt dislodgements across its 14.6 KM length from past data and using suitable ML software, prediction of belt dislodgements should be done beforehand to take corrective and preventive actions."}
{"id": "SIH1506", "title": "Leveraging the power of deep learning to overcome the challenges of marine engineering and improve vessel operations", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Ports, Shipping and Waterways", "text": "Title: Leveraging the power of deep learning to overcome the challenges of marine engineering and improve vessel operations. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Ports, Shipping and Waterways. Description: Developing an artificial neural network for the marine industry presents significant challenges, including limited computing power, unreliable communication infrastructure, low availability of data and complex vessel systems. The goal of this problem is to design and develop an effective AI solution using deep neural networks that can optimize vessel performance, reduce operational costs, and improve safety in the context of merchant vessel operations. The solution must be able to demonstrate high accuracy, robustness, and scalability, while also addressing the unique challenges faced by the marine industry. Participants are encouraged to explore novel approaches to training and deployment, including techniques for data processing, feature extraction, and model optimization."}
{"id": "SIH1505", "title": "Aerial Location of Hazardous Atmosphere in Industries", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: Aerial Location of Hazardous Atmosphere in Industries. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Home Affairs. Description: Fire Prevention is better than Firefighting. With an increase in Industrialization and globalisation, there has been a spate of fire accidents occurring in the past few years. Every fire incident in industries causes massive losses to the assets of the nation. There are various cases of fire which caused enormous loss of property and human life, like the IOCL Jaipur fire in 2009, NTPC Power Plant Explosion, Unchahar in 2017, and various others. As per the National Crime Records Bureau, over 60 people die every day in India due to fire. In IOCL 2009 fire itself, the industry suffered a loss of thousands of crores. To prevent such incidents and minimize such economic losses, all industries need to have a Fire Pre Plan which should be a part of the Emergency Response and Disaster Management Plan ERDMP, and the extent of a fire hazard which includes Flammability Area, Toxic Area and Threat zone calculation is instrumental in designing the Fire PrePlan for any industry using Areal Location of Hazardous Atmosphere."}
{"id": "SIH1504", "title": "Capacity building, performance assessment and motivation driven tool for faculty upgradation", "category": "Smart Education", "subcategory": "Software", "organization": "Government of Jharkhand", "text": "Title: Capacity building, performance assessment and motivation driven tool for faculty upgradation. Category: Smart Education. Subcategory: Software. Organization: Government of Jharkhand. Description: If teachers were given more freedom, as would likely happen if a blended learning model were introduced as mentioned above, innovation would rocket, and an education system overhaul isnt exactly necessary to accomplish that. After all, there are and will always be plenty of options open to teachers to introduce more creativity and innovation to their lessons. Some of the options include Learning Management System LMS, which allows teachers to create their own personalized, interactive courses from content provided by a range of publishers. Students can then access these courses on any device, while the app keeps a record of how they are progressing. If students are struggling. the course can quickly be altered. If students struggle with boredom, gamebased learning can be introduced with LMS. Although granting teachers access to tablets and smartboards may help boost their comfort with education technology, many teachers simply have not thought about how they can best utilize technology in their curriculum. Indeed, the way a history teacher utilizes laptops in the classroom may be very different than the way a math teacher utilizes a smartboard. Both likely require plenty of time for trial, error, and experimentation to bring their lesson plans up to date. A major challenge in the adoption of new tools is not providing teachers with the guidance they need to make education technology work for them in their specific classroom. To overcome the challenge, it is necessary to have ERP solution for the teachers with adequate credit mechanism which can used for appraisals and performancebased incentives to the teachers. Experts"}
{"id": "SIH1503", "title": "Automatic Drug Dispenser", "category": "MedTech  BioTech  HealthTech", "subcategory": "Hardware", "organization": "Ministry of AYUSH", "text": "Title: Automatic Drug Dispenser. Category: MedTech  BioTech  HealthTech. Subcategory: Hardware. Organization: Ministry of AYUSH. Description: The neverending queue in hospitals is a nuisance that patients have to go through. Along with the problems being faced by patients due to suffering from diseases, standing and waiting in long queues adds on as a challenge for them to face. This leads to inconvenience to patients at physical as well as mental level. Although in this direction, some steps have been taken by the hospital management system which makes it convenient for patients to consult with doctors by registering and taking an appointment prior to the visit to hospital. But, patients still face inconvenience at the mediciner Developing a QR Code drug ATM muy resolve the situation at hand, there Page 6  ents. A QR code in the prescription will be used to dish out the right drug from the ATM. The drug ATM should be able to dispense wide range of Ayush Medicines available in a hospitalpharmacy."}
{"id": "SIH1502", "title": "Blended Learning to overcome inadequate infrastructure", "category": "Smart Education", "subcategory": "Software", "organization": "Government of Jharkhand", "text": "Title: Blended Learning to overcome inadequate infrastructure. Category: Smart Education. Subcategory: Software. Organization: Government of Jharkhand. Description: Once the class size passes a certain point, the teachers are bound to fail because the demands on their time cannot be met. In essence, the root of this problem is not the number of children in a classroom but rather the inability for each child to receive adequate attention. Combining modern technology and 11 tuition, students could perform far beyond other students who were being taught in more conventional ways. By combining blended learning where facetoface teaching is combined with online learning with realtime data, we can get rapid feedback in classrooms and use that feedback to further enhance the quality of education. As an example, consider a classroom of 30 students. Ten students with similar abilities may work closely with the teacher, another ten may work through lectures and online tasks using computer terminals, and the final ten may work together on a group project. In the next lesson, students are rotated so they can learn in different ways throughout the course. This kind of approach enables the teacher to focus more closely on fewer children at once. The teacher can also tailor the learning approach for each student based on how well each one works for the individual. Meanwhile, the software on the computer is advanced enough to tailor the content to each student as well. By collecting realtime feedback on each childs results, the course contents can be adapted per student and make it as if they were receiving a oneonone tuition. This approach allows each student to have their own learning path thats customized to their needs. By doing this, teachers can easily see which students are falling behind and offer more individualized teaching to those students. If more schools were to adopt a similar approach, where some responsibilities could be handled by techaided learning methods, more of the teachers time could be freedup to give more attention where its needed the most, even in larger classroom sizes. Further the objective is to come up with ways and means to make meaningful digital applications that can use this information and generate knowledge, spread massive awareness, and support education delivery effectiveness of students into educational ecosystem and generate insights based on data it collected. The objective of this problem statement solution is also to come up with innovative solution which may use digital and physical medium together to make sure specially abled students who are slow learners are identified, their progress is monitored and right alarms and notifications are generated to assist keeping their education on track with elevated motivation. The solution may use technologies like Artificial intelligence, machine learning and internet of things and blockchain to ensure specially abled children are given right systems and interfaces to learn and grow. This solutions objective is to make sure learning becomes seamless and peaceful with right insights and aids for the children."}
{"id": "SIH1501", "title": "Development of Virtual Reality VR technology for Dhatuposhana Nyaya", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: Development of Virtual Reality VR technology for Dhatuposhana Nyaya. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of AYUSH. Description: There are primerly three tissue nourishment theories known as Dhatuposhan Nyaya described by Acharyas. They are as follow 1. Ksheeradadhi Nyaya Theory of Transformation This is compared to the conversion of milk ksheera into curd dadhi in entirety. This complete transformation of one Dhatu to another is called Sarvatma Parinama total bio conversion. According to this theory, nutrients getting converted into tissues. 2.Kedarikulya Nyaya Theory of Transportation According to this theory, nourishment of the tissues can be compared to irrigation of fields by water from a canal. This explains supply of nutrients through circulatory system. 3.Khalckapota Nyaya Theory of Selection Like pigeons pecking the grains from a threshing floor and carrying it back to their nests depend on the direction and the time required for them to travel. The nutrition required by a dhatu tissue is selected from the essence part of food being circulated. To understand and apply these principlesprocess at tissue level scientifically Virtual Reality VR technology may be helpful."}
{"id": "SIH1500", "title": "Education ecosystem for specially abled student need provision and improvement to take care of compliance, governance and conduct.", "category": "Smart Education", "subcategory": "Software", "organization": "Government of Jharkhand", "text": "Title: Education ecosystem for specially abled student need provision and improvement to take care of compliance, governance and conduct.. Category: Smart Education. Subcategory: Software. Organization: Government of Jharkhand. Description: Children with disabilities in minor or major forms for any of the physical organs or mentally unstable children are considered to be speciallyabled ones and to help them absorb the natural flow of life, we need to consider and provide them with a supportive and growing environment where they are able to live and grow. In the educational ecosystem, the needs of such an environment need to be catered according to the category of ailments that exist in the students and provisions need to be made in order to make them feel special, and cared for and set the right mindset while they grow. There are very few educational institutes in India, that accept speciallyabled students in regular streams making them feel one among natural children. The first and foremost need is to comply with the Disability Act provisions in the Indian education ecosystem and have the right provisions such as physical infrastructure, digital interaction pathways and the right tooling to make this happen in a much larger portion of the Indian education industry. This problem statement hovers more around the lack of the right system and governance in the Indian Education industry and also has to deal with Disability students intake, progress and outgo after the education completion. The State Education Report for India 2019 Children with Disabilities by UNESCO states that 27 of disabled children between the age of 519 years dont have access to education. It also says that nearly 34th of 5 years old speciallyabled are left out of the education system, further revealing that this ratio varies genderwise and girls tend to show in high numbers when it comes to depriving education. As per WHO report, over 1 billion people in the world are specially abled contributing to 15 of the total population and WHODAS 2.0 accessibility standards define means of assessing the disability in an individual, which ponder on a lot of aspects such as, it has been felt that differentlyabled persons need special arrangements in the environment for their mobility and independent functioning. It is also a fact that many institutes have architectural barriers that disabled persons find difficult for their daytoday functioning. The colleges are expected to address accessibilityrelated issues as per the stipulations of the Persons with Disabilities Act 1995 and ensure that all existing structures as well as future construction projects in their campuses are made disabledfriendly. The institutes should create special facilities such as ramps, rails and special toilets, and make other necessary changes to suit the special needs of differentlyabled persons. The construction plans should clearly address the accessibility issues pertaining to disability. Guidelines on accessibility are laid out by the office of the Chief Commissioner of Disabilities. Consciously improving Education for speciallyabled children will need planned efforts with the help of technology and human power. This makes the problem statement quite challenging and critical to be addressed the special compliance and governance needs. This problem statement needs holistic solutions that may be hybrid in nature. The objective of this problem statement in one way could be the right implementation of the Disability Act and the provisioning of digital infrastructure and applications to address the needs and feedback mechanisms. Facilitate admission of differentlyabled persons in various courses, provide guidance and counselling to differentlyabled individuals, Create awareness about the needs of differentlyabled persons, and other general issues concerning their learning. Assist differentlyabled graduates to gain successful employment in the public as well as private sectors, designing special interaction gateways for such children and helping them feel natural while growing is important. Further, the objective is to come up with ways and means to make meaningful digital applications that can use this information and generate knowledge, spread massive awareness, and support the intake and management of speciallyabled children into the educational ecosystem. The objective of this problem statement solution is also to come up with innovative solutions which may use digital and physical mediums together to make sure disabled persons are able to access and operate these applications. The solution may use technologies like Artificial intelligence, machine learning and internet of things and blockchain to ensure speciallyabled children are given the right systems to get into the mainstream of education, sustain with growth aspects and be placed well after their education is completed. It is all about following laws and legal procedures to comply with the Disability Act and make sure the system is governed well for speciallyabled peoples needs in their complete education lifecycle."}
{"id": "SIH1499", "title": "Green options for milk packaging Low cost, environmentfriendly, and extended shelf life packaging for milk", "category": "Renewable  Sustainable Energy", "subcategory": "Hardware", "organization": "Ministry of Fisheries, Animal Husbandry and Dairying", "text": "Title: Green options for milk packaging Low cost, environmentfriendly, and extended shelf life packaging for milk. Category: Renewable  Sustainable Energy. Subcategory: Hardware. Organization: Ministry of Fisheries, Animal Husbandry and Dairying. Description: Currently, most of the liquid milk packaging pouch milk in India, uses plastic film. Disposal of the plastic packs has huge environmental concerns and we must seek out for more environmentfriendly alternatives. As a step towards reducing carbon footprint options like bioplastics, biodegradable plastics, organic fibres, or any other material, etc. may be explored. Such alternatives must be easy to use for the industry  distribution chain, ensure food safety, safeguard against contaminationleechingmicrobial attack, cheap low cost and also help in improving shelf life of milk."}
{"id": "SIH1498", "title": "Develop a Proctored exam tool for shortlisting the candidates for the national and international level hackathons.", "category": "Smart Automation", "subcategory": "Software", "organization": "AICTE, MIC", "text": "Title: Develop a Proctored exam tool for shortlisting the candidates for the national and international level hackathons.. Category: Smart Automation. Subcategory: Software. Organization: AICTE, MIC. Description: The problem at hand is to develop an efficient and reliable Proctored Exam Tool that automates the proctoring process and enables institutions, organizations, and testing agencies to administer secure and scalable remote examinations. The Proctored Exam Tool should aim to address the following challenges 1. Remote Monitoring The software should allow proctors to remotely observe the examtakers via webcam and screen sharing to ensure that no unauthorized assistance is provided during the exam. 2. Identity Verification Implement a robust identity verification mechanism to ensure that the examtaker is the intended praticipants, preventing impersonation and fraud. 3. Cheating Prevention Incorporate advanced monitoring features, such as realtime AIbased facial recognition, eye tracking, and suspicious behaviour detection, to identify and flag potential instances of cheating. 4. Secure Environment Ensure that the exam environment remains secure by disabling access to unauthorized resources like other applications, websites, or external devices during the examination. 5. Data Privacy The tool should adhere to stringent data privacy and security standards to protect sensitive information of both examtakers and organisers. 6. Userfriendly Interface Design an intuitive and userfriendly interface for both proctors and examtakers to facilitate easy navigation and seamless exam administration. 7. Scalability The software should be capable of handling a large number of concurrent examtakers without compromising on performance or security. 8. Compatibility Ensure compatibility with different operating systems, browsers, and devices to accommodate diverse user preferences and accessibility needs. 9. Integration The Proctored Exam Tool should be able to integrate with various application and assessment platforms commonly used in institutions, organizations, and testing agencies. 10. Reporting and Analytics Provide comprehensive exam reports and analytics to assist institutions in evaluating exam performance, identifying trends, and making datadriven decisions."}
{"id": "SIH1497", "title": "Student Innovation", "category": "Toys  Games", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Toys  Games. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Challenges your creative minds to conceptualize and develop unique toys and games."}
{"id": "SIH1496", "title": "Student Innovation", "category": "Toys  Games", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Toys  Games. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Challenges your creative minds to conceptualize and develop unique toys and games."}
{"id": "SIH1495", "title": "Student Innovation", "category": "Smart Automation", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Smart Automation. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Ideas focused on the intelligent use of resources for transforming and advancements of technology with combining the artificial intelligence to explore more various sources and get valuable insights."}
{"id": "SIH1494", "title": "Student Innovation", "category": "Fitness  Sports", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Fitness  Sports. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Ideas that can boost fitness activities and assist in keeping fit."}
{"id": "SIH1493", "title": "Student Innovation", "category": "Heritage  Culture", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Heritage  Culture. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Ideas that showcase the rich cultural heritage and traditions of India"}
{"id": "SIH1492", "title": "Student Innovation", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Cuttingedge technology in these sectors continues to be in demand. Recent shifts in healthcare trends, growing populations also present an array of opportunities for innovation."}
{"id": "SIH1491", "title": "Student Innovation", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Agriculture, FoodTech  Rural Development. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Developing solutions, keeping in mind the need to enhance the primary sector of India  Agriculture and to manage and process our agriculture produce"}
{"id": "SIH1490", "title": "Student Innovation", "category": "Smart Vehicles", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Smart Vehicles. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Creating intelligent devices to improve the commutation sector"}
{"id": "SIH1489", "title": "Student Innovation", "category": "Transportation  Logistics", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Transportation  Logistics. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Submit your ideas to address the growing pressures on the citys resources, transport networks, and logistic infrastructure"}
{"id": "SIH1488", "title": "Student Innovation", "category": "Robotics and Drones", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Robotics and Drones. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: There is a need to design drones and robots that can solve some of the pressing challenges of India such as handling medical emergencies, search and rescue operations, etc."}
{"id": "SIH1487", "title": "Student Innovation", "category": "Clean  Green Technology", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Clean  Green Technology. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Solutions could be in the form of waste segregation, disposal, and improve sanitization system."}
{"id": "SIH1486", "title": "Student Innovation", "category": "Travel  Tourism", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Travel  Tourism. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: A solutionidea that can boost the current situation of the tourism industries including hotels, travel and others."}
{"id": "SIH1485", "title": "Student Innovation", "category": "Renewable  Sustainable Energy", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Renewable  Sustainable Energy. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Innovative ideas that help manage and generate renewable sustainable sources more efficiently."}
{"id": "SIH1484", "title": "Student Innovation", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Provide ideas in a decentralized and distributed ledger technology used to store digital information that powers cryptocurrencies and NFTs and can radically change multiple sectors"}
{"id": "SIH1483", "title": "Student Innovation", "category": "Miscellaneous", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Miscellaneous. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Technology ideas in tertiary sectors like Hospitality, Financial Services, Entertainment and Retail."}
{"id": "SIH1482", "title": "Student Innovation", "category": "Smart Automation", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Smart Automation. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Ideas focused on the intelligent use of resources for transforming and advancements of technology with combining the artificial intelligence to explore more various sources and get valuable insights."}
{"id": "SIH1481", "title": "Student Innovation", "category": "Fitness  Sports", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Fitness  Sports. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Ideas that can boost fitness activities and assist in keeping fit."}
{"id": "SIH1480", "title": "Student Innovation", "category": "Heritage  Culture", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Heritage  Culture. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Ideas that showcase the rich cultural heritage and traditions of India"}
{"id": "SIH1479", "title": "Student Innovation", "category": "MedTech  BioTech  HealthTech", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: MedTech  BioTech  HealthTech. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Cuttingedge technology in these sectors continues to be in demand. Recent shifts in healthcare trends, growing populations also present an array of opportunities for innovation."}
{"id": "SIH1478", "title": "Student Innovation", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Agriculture, FoodTech  Rural Development. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Developing solutions, keeping in mind the need to enhance the primary sector of India  Agriculture and to manage and process our agriculture produce"}
{"id": "SIH1477", "title": "Student Innovation", "category": "Smart Vehicles", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Smart Vehicles. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Creating intelligent devices to improve the commutation sector"}
{"id": "SIH1476", "title": "Student Innovation", "category": "Transportation  Logistics", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Transportation  Logistics. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Submit your ideas to address the growing pressures on the citys resources, transport networks, and logistic infrastructure"}
{"id": "SIH1475", "title": "Student Innovation", "category": "Robotics and Drones", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Robotics and Drones. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: There is a need to design drones and robots that can solve some of the pressing challenges of India such as handling medical emergencies, search and rescue operations, etc."}
{"id": "SIH1474", "title": "Student Innovation", "category": "Clean  Green Technology", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Clean  Green Technology. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Solutions could be in the form of waste segregation, disposal, and improve sanitization system."}
{"id": "SIH1473", "title": "Student Innovation", "category": "Travel  Tourism", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Travel  Tourism. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: A solutionidea that can boost the current situation of the tourism industries including hotels, travel and others."}
{"id": "SIH1472", "title": "Student Innovation", "category": "Renewable  Sustainable Energy", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Renewable  Sustainable Energy. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Innovative ideas that help manage and generate renewable sustainable sources more efficiently."}
{"id": "SIH1471", "title": "Student Innovation", "category": "Blockchain  Cybersecurity", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Blockchain  Cybersecurity. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Provide ideas in a decentralized and distributed ledger technology used to store digital information that powers cryptocurrencies and NFTs and can radically change multiple sectors"}
{"id": "SIH1470", "title": "Student Innovation", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Miscellaneous. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Technology ideas in tertiary sectors like Hospitality, Financial Services, Entertainment and Retail."}
{"id": "SIH1469", "title": "Student Innovation", "category": "Disaster Management", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Disaster Management. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Disaster Management includes ideas related to risk mitigation and Planning before,after or Duration of Disaster."}
{"id": "SIH1468", "title": "Student Innovation", "category": "Disaster Management", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Disaster Management. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Disaster Management includes ideas related to risk mitigation and Planning before,after or Duration of Disaster."}
{"id": "SIH1467", "title": "Student Innovation", "category": "Smart Education", "subcategory": "Software", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Smart Education. Subcategory: Software. Organization: AICTE, MICStudent Innovation. Description: Smart Education, a Concept that Describes learning in digital age.it enables learner to learn more effectively,efficently,flexibly and comfortably."}
{"id": "SIH1466", "title": "Student Innovation", "category": "Smart Education", "subcategory": "Hardware", "organization": "AICTE, MICStudent Innovation", "text": "Title: Student Innovation. Category: Smart Education. Subcategory: Hardware. Organization: AICTE, MICStudent Innovation. Description: Smart Education, a Concept that Describes learning in digital age.it enables learner to learn more effectively,efficently,flexibly and comfortably."}
{"id": "SIH1465", "title": "A unified portal for developing a model Curriculum for all the AICTE Approved Institutes", "category": "Miscellaneous", "subcategory": "Software", "organization": "AICTE", "text": "Title: A unified portal for developing a model Curriculum for all the AICTE Approved Institutes. Category: Miscellaneous. Subcategory: Software. Organization: AICTE. Description: The All India Council for Technical Education AICTE is responsible for formulating and maintaining the curriculum standards for technical education in India. A unified portal is required to collaborate with educational experts, curriculum designers to provide the functionality and designing a model curriculum for all the Institutes. The main functionality provide by portal are 1. User Registration and Authentication Allow users educators, curriculum developers, administrators to create accounts and authenticate their identity to access the portals features. 2. Dashboard and User Profiles Provide a personalized dashboard for each user, displaying relevant information and options based on their role and preferences. Users can manage their profiles, track progress, and access saved curriculum designs. 3. Curriculum Design Tools Offer intuitive tools and templates for curriculum design, including the ability to define course objectives, outline modules, specify learning outcomes, and create assessment methods. Users should be able to organize and structure curriculum content efficiently. 4. Resource Repository Maintain a repository of educational resources, such as textbooks, articles, videos, and interactive materials, categorized by subjects and topics. Users can search, access, and incorporate these resources into their curriculum designs. 5. Collaboration and Feedback Facilitate collaboration among users by allowing them to invite others to review and provide feedback on their curriculum designs. Incorporate features like commenting, version control, and document sharing to enhance collaboration and streamline the review process. 6. Analytics and Reporting Provide analytics and reporting features to track and analyze the effectiveness of curriculum designs. This can include metrics like student performance, course completion rates, and assessment outcomes. Generate reports and visualizations to aid decision making and improvement. 7. Notifications and Updates Notify users about important updates, changes in educational policies, or new resources available on the portal. Allow users to subscribe to relevant topics or receive notifications based on their preferences. 8. Scalability and Security Design the portal to handle a large user base and ensure data security. Implement appropriate security measures to protect user information, curriculum designs, and sensitive data."}
{"id": "SIH1464", "title": "Design and Developed a personalized online meeting system for AICTE", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "AICTE", "text": "Title: Design and Developed a personalized online meeting system for AICTE. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: AICTE. Description: In AICTE many online meetings are conducted to various stakeholders of AICTE such as Institutes, faculty members, HOD and in the Ministry also. Lot of confidential data has been shared through these online meeting. In order to increase security and to make a robust system a personalized online meeting portal is needed in AICTE. 1. Based on the requirements, design the architecture of the online meeting system. Determining the components, such as the server infrastructure, database, APIs, and user interfaces, that will be needed to develop the system. 2. Develop the backend infrastructure Build the backend infrastructure that will handle the core functionalities of the online meeting system. This typically includes user management, meeting scheduling, realtime communication, file sharing, and any custom features required in AICTE. 3. Implement video conferencing capabilities Integrate video conferencing functionality into the system. Web RealTime Communication technology to facilitate realtime video and audio communication between participants. 4. Create a userfriendly interface for participants to join meetings, manage settings, access recordings, and utilize additional features. 5. Implement robust security measures to protect the online meetings and user data. This includes encryption of data in transit and at rest, user authentication and access controls, secure storage of meeting recordings, and adherence to privacy regulations."}
{"id": "SIH1463", "title": "Establishing a Server log system for Egovernance with appropriate notification and content based classification of log AICTE", "category": "Miscellaneous", "subcategory": "Software", "organization": "AICTE", "text": "Title: Establishing a Server log system for Egovernance with appropriate notification and content based classification of log AICTE. Category: Miscellaneous. Subcategory: Software. Organization: AICTE. Description: A server log system, also known as a log management system, is a software tool that records and stores log messages generated by various components and applications running on a server. It provides a centralized location for collecting, analyzing, and managing logs to monitor and troubleshoot system issues and performance problems. Here are some basic Requirement of a server log system 1. Log Collection The system collects log data from various sources, including operating systems, applications, services, and network devices. It can gather logs from different servers and consolidate them into a central repository. 2. Log Storage Log entries are typically stored in a structured format, such as plain text files or a database. The system may also employ compression and archival mechanisms to optimize storage space and retain logs for an extended period. 3. Log Analysis and Search The log system enables searching, filtering, and querying log data based on specific criteria, such as time range, log level, source, or custom keywords. This functionality helps in identifying patterns, anomalies, or events of interest. 4. Alerting and Notifications The system can be configured to send notifications or alerts based on predefined rules or thresholds. For example, it can notify administrators when critical errors occur or securityrelated events are detected in the logs. 5. Log Visualization and Reporting The log systems provide visual representations of log data, such as charts, graphs, and dashboards. Visualizations will help in gaining insights, identifying trends, and generating reports for auditing, compliance, or performance analysis. 6. Log Retention and Compliance Server logs retained for a certain period to comply with regulatory requirements or internal policies. The log system should support flexible retention periods and secure storage to meet these obligations. 7. Integration and APIs A robust log system offers APIs and integrations with other tools and systems. This allows developers and administrators to automate log analysis, integrate with security information and event management SIEM systems, or build custom solutions on top of the log data. So the developed software should provide the following features to the enduser 8. Server Provisioning The system facilitates the deployment and provisioning of servers, including hardware configuration, operating system installation, and initial software setup. It may include tools for automated server deployment and configuration management. 9. Monitoring and Performance Management Server management systems monitor the health, performance, and resource utilization of servers. They collect and analyze data on CPU usage, memory usage, disk space, network traffic, and other metrics. Alerts and notifications are generated when predefined thresholds are exceeded. 10. Patch Management and Updates The system ensures that servers are kept up to date with security patches, bug fixes, and software updates. It automates the process of identifying, downloading, testing, and applying updates to minimize vulnerabilities and improve server stability. 11. Configuration Management Server management systems help administrators maintain consistent configurations across servers. They provide tools for managing and enforcing server configurations, including software installations, network settings, user accounts, and security policies. 12. Backup and Disaster Recovery The system facilitates regular backups of server data and configurations to protect against data loss and facilitate disaster recovery. It may include features for scheduling backups, managing backup storage, and performing recovery operations in case of system failures. 13. Logging and Auditing The system captures and stores logs generated by servers for troubleshooting, compliance, and auditing purposes. It provides tools for analyzing logs, searching for specific events, and generating reports on server activities. 14. Resource Allocation and Load Balancing Server management systems help optimize resource allocation and load balancing across servers. They can monitor server workloads, allocate resources based on demand, and distribute incoming network traffic efficiently."}
{"id": "SIH1462", "title": "Awareness and Preparedness Towards Disaster Management", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: Awareness and Preparedness Towards Disaster Management. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Home Affairs. Description: Disasters can strike anytime, anywhere, and can have devastating effects on individuals, families, communities, and entire countries. Therefore, its crucial to have awareness and preparedness towards disaster management. The mock exercises. School Safety Programmes SSP, and Community Awareness programme CAP that will be conducted by various stakeholders such as NDRFSDRF, etc. These exercises are aimed at enhancing the preparedness and response capabilities of our community to deal with any emergency or disaster situation. The exercises are designed to simulate reallife scenarios and test the response capabilities of various agencies involved in disaster management. These exercises are crucial in identifying gaps in our preparedness and response capabilities and help in improving them To benefit from these programs, it is important to give a notification regarding mock exercises, SSP, and CAP conducted by various stakeholders such as NDRFSDRF, etc., to concern people so that they registered timely and became a beneficiary a part of the exercise. This will enable to concern to be prepared for any eventualities that may occur in the future. The system provides the contact number, links, and information regarding Disaster Management Authorities. Datasheet of various previous disasters, dos, and donts, it may include early warning of disaster."}
{"id": "SIH1461", "title": "Cybersecurity Portal for Effective Management of Servers and Firewalls", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "AICTE", "text": "Title: Cybersecurity Portal for Effective Management of Servers and Firewalls. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: AICTE. Description: The All India Council for Technical Education AICTE is responsible for managing and safeguarding critical infrastructure and data related to technical education institutions across India. To ensure robust cybersecurity measures, AICTE requires a centralized and comprehensive portal dedicated to managing servers, firewalls, load balancers, software licenses, user access, and other data center hardware components is commonly known as a Data Center Management Portal or Data Center Infrastructure Management DCIM Portal. However, the current infrastructure management practices face various challenges that hinder efficient and secure operations. The existing problem can be defined by the following factors Fragmented Infrastructure Management AICTEs infrastructure management practices may be fragmented, resulting in disparate systems and tools for managing servers, firewalls, load balancers, and other hardware components. This lack of centralized control leads to inefficiencies, inconsistencies, and potential security vulnerabilities. 1. Manual and TimeConsuming Processes The absence of an integrated portal leads to manual and timeconsuming processes for managing various infrastructure components. Activities such as provisioning, monitoring, patching, and license management are often performed manually, consuming significant human resources and increasing the risk of errors or oversights. 2. Limited Visibility and Control Without a dedicated portal, AICTE may face challenges in gaining comprehensive visibility into the status, performance, and security of servers, firewalls, load balancers, and other hardware components. This limitation hampers effective monitoring, maintenance, and proactive identification of potential security threats or vulnerabilities. 3. Compliance and License Management The absence of a centralized portal makes it difficult to track and manage software licenses and ensure compliance with licensing agreements. This may result in the misuse of licenses, unintentional noncompliance, or unnecessary expenses due to duplicate purchases or inadequate license usage tracking. 4. User Access Management AICTE needs an efficient mechanism to manage user access to various infrastructure components. This includes defining user roles and permissions, ensuring secure authentication and authorization mechanisms, and maintaining an auditable log of user activities. Without a dedicated portal, managing user access becomes challenging and increases the risk of unauthorized access or privilege abuse. Addressing these challenges and implementing an AICTE Cybersecurity Portal has these specific feature need to be developed 5. Server Management The portal allows administrators to manage servers, including provisioning, configuration, monitoring, and maintenance tasks. It provides an overview of server health, resource utilization, and performance metrics. 6. Firewall and Network Device Management The portal enables management and configuration of firewalls, switches, routers, and other network devices. It provides a central interface to set up and monitor network policies, security rules, and traffic management. 7. Load Balancer Management Load balancers play a critical role in distributing network traffic across multiple servers. The portal allows configuration, monitoring, and scaling of load balancers to ensure optimal performance, high availability, and efficient resource utilization. 8. Software License Management It provides a centralized repository to manage software licenses for various applications and operating systems. The portal helps track license usage, compliance, renewal dates, and license allocation to specific servers or users. 9. User Access and Identity Management The portal facilitates user access control, authentication, and authorization. Administrators can define user roles, permissions, and access levels for different components and resources within the data center environment. 10. Hardware Inventory and Asset Tracking The portal maintains an inventory of data center hardware components, including servers, switches, firewalls, load balancers, and more. It tracks hardware configurations, warranties, and maintenance schedules, helping with resource planning and optimizing hardware lifecycle management. 11. Monitoring and Alerting The portal offers realtime monitoring of hardware components, system performance, and environmental conditions e.g., temperature, humidity. It generates alerts and notifications for issues such as hardware failures, network disruptions, or abnormal resource consumption. 12. Reporting and Analytics The portal provides reporting and analytics capabilities to generate insights into data center operations, resource utilization, and performance metrics. It offers dashboards, charts, and customizable reports to aid in decisionmaking, capacity planning, and troubleshooting. 13. Integration and Automation The portal integrates with other management tools and systems within the data center, such as ticketing systems, configuration management databases CMDB, or IT service management ITSM platforms. It also supports automation capabilities through APIs, allowing for seamless integration with existing workflows and processes. 14. Security and Compliance The portal includes security features to ensure secure access, data protection, and compliance with industry standards and regulations. It may include features like rolebased access control RBAC, audit logs, and encryption of sensitive data. A welldesigned Data Center Management Portal helps streamline operations, improve efficiency, and enhance the overall management of data center infrastructure and resources. It centralizes critical functions and provides a holistic view of the data center environment, enabling administrators to efficiently manage and monitor hardware components, network devices, user access, and software licenses."}
{"id": "SIH1460", "title": "Integration of AI for Adaptive Learning for MCQ Selection in PARAKH", "category": "Smart Automation", "subcategory": "Software", "organization": "AICTE", "text": "Title: Integration of AI for Adaptive Learning for MCQ Selection in PARAKH. Category: Smart Automation. Subcategory: Software. Organization: AICTE. Description: All India Council for Technical Education AICTE has implemented a largescale program to assess and improve the skills of engineering students in India in AICTE Approved Technical Institutions across the country. In respect of analysis, each of the online assessments including those testing academic skills and those testing higher order thinking skills is used for comparing student outcomes. More specifically, each of the Online assessments will be considered as unidimensional, reliable, and good at measuring a range of student ability. The vast majority of the items should also demonstrate psychometric characteristics of students. A Questions bank is already prepared and random selection of questions has been selected for the assessment. However, for the further development PARAKH teams needs a webbased adaptive multiplechoice question MCQ testing system that delivers MCQ assessments to users over the internet and adapts the questions based on the users performance and abilities. A Preassessment or initial knowledge assessment can be designed to gauge the users baseline knowledge an initial assessment may be conducted. This assessment can consist of a set of MCQs covering different topics or difficulty levels. Based on that result a final assessment can be prepared. The system employs an adaptive algorithm that analyzes the users responses, time spent on each question and level of difficulty and number of time questions has been asked in the past and also for performance data from the preassessment to determine their knowledge level and proficiency in specific areas. The algorithm uses this information to select subsequent questions that appropriately assessment to the students. Based on the adaptive algorithm, the system selects the next set of MCQs from a question bank. The questions chosen may vary in difficulty and content based on the students performance. For example, if the student answers a question correctly, the platform may present a more challenging question next, while an incorrect answer may result in an easier question. AI based question generation should be integrated in the portal so that no random question is picked from the database and overall performance will be low."}
{"id": "SIH1459", "title": "Integration of Multiple Databases of AICTE in order to fetch Coherent Information", "category": "Miscellaneous", "subcategory": "Software", "organization": "AICTE", "text": "Title: Integration of Multiple Databases of AICTE in order to fetch Coherent Information. Category: Miscellaneous. Subcategory: Software. Organization: AICTE. Description: The All India Council for Technical Education AICTE is responsible for overseeing and regulating technical education in India. As part of its functions, AICTE manages and maintains data related to various technical institutions across the country. This data includes information about colleges, courses, faculty, scholarships, approvals and other relevant schemes. One of the challenges faced by AICTE is the need to efficiently search and retrieve information from multiple databases. With a vast amount of data spread across numerous databases, it becomes crucial to have a robust and reliable search engine system in place. However, the current search mechanisms employed by AICTE encounter several limitations and inefficiencies, hampering the process of extracting relevant information. The existing problem arises due to the following factors 1. Fragmented Database Structure 2. Limited Search Capability 3. Slow Search Speed 4. Data Consistency and Accuracy Addressing these challenges and implementing an improved search system for multiple databases can provide AICTE with the following benefits Enhanced Search Accuracy and Relevance A robust search system with advanced search algorithms and indexing techniques can significantly improve the accuracy and relevance of search results, allowing AICTE to access the most relevant information efficiently. 1. Increased Search Speed and Efficiency By optimizing the search algorithms and leveraging efficient indexing methods, AICTE can achieve faster search speeds, reducing the time required to retrieve data from multiple databases and improving overall operational efficiency. 2. Improved Data Consistency and Quality Implementing mechanisms to ensure data consistency and accuracy across databases will enhance the reliability and integrity of search results. Regular data synchronization and quality checks can eliminate inconsistencies and provide users with reliable and uptodate information. 3. Scalability and FutureReadiness Designing the search system with scalability in mind will enable AICTE to accommodate the growth of data and seamlessly integrate new databases as the technical education landscape evolves. This futurereadiness will ensure that the search system remains efficient and effective in the long term. In conclusion, developing a comprehensive SEARCH engine system for multiple databases will address the limitations of the current search mechanisms employed by AICTE."}
{"id": "SIH1458", "title": "AI Based  Designing of Assistive portal for Stakeholders in Approval Process", "category": "Miscellaneous", "subcategory": "Software", "organization": "AICTE", "text": "Title: AI Based  Designing of Assistive portal for Stakeholders in Approval Process. Category: Miscellaneous. Subcategory: Software. Organization: AICTE. Description: The All India Council for Technical Education AICTE plays a crucial role in approving and regulating technical education institutions in India. The approval process ensures that institutions meet the required standards and guidelines set by AICTE, ensuring quality education and a standardized framework. However, the current approval process faces several challenges, which hinder its efficiency and effectiveness. 1. Complex Approval Process Structure 2. Difficulty in Understanding of Approval Process Handbook 3. Complex Documentation Requirements Conclusion Implementing a Comprehensive Portal for Simplified Understanding of AICTEs Approval Process Handbook and generate the final fee submission to all the stakeholders In conclusion, a viable solution to address the difficulties in understanding AICTEs Approval Process Handbook is to develop a dedicated portal that streamlines the approval process for technical institutions. This portal would allow institutes and stakeholders to input their specific course, infrastructural requirement and in return, provide the all the required documents, along with fee structure information. The fundamental objective of this portal is to parse the approval process handbook and convert its contents into a userfriendly format that facilitates easy comprehension and understanding of AICTEs requirements. By implementing such a portal, the following benefits can be achieved 1. Enhanced Clarity and Ease of Understanding The portal would transform the complex language and technical jargon of the Approval Process Handbook into simplified and easily understandable information. Institutes and stakeholders would have a clear understanding of the specific requirements and guidelines outlined by AICTE, facilitating efficient compliance. 2. Customized Guidance and Relevant Documentation The portal would dynamically generate the required documentation templates, and fee structures based on the specific course requirements inputted by the institute. This tailored approach ensures that the information provided is directly relevant to the institutes needs, streamlining the approval process. 3. Time and Resource Efficiency The portal would significantly reduce the time and effort required for institutes to interpret and navigate the Approval Process Handbook. By automating the parsing and presentation of information, the portal expedites the understanding of AICTEs requirements, saving valuable resources for both AICTE and the institutes. 4. Increased Accuracy and Compliance With a userfriendly portal providing comprehensive and clear instructions, institutes are more likely to adhere accurately to AICTEs guidelines. This would result in improved compliance and higherquality submissions, reducing the need for reworks and ensuring smoother and faster approval processes. 2 5. Continuous Updates and Realtime Assistance The AI based portal can be regularly updated to reflect any changes or amendments in the Approval Process Handbook. Additionally, it can provide realtime assistance and support, offering clarification on specific queries or concerns raised by institutes during the approval process. By developing a comprehensive portal that effectively parses and presents the Approval Process Handbook, AICTE can empower institutes and stakeholders to easily understand and comply with the requirements. This would foster transparency and assistance to the approval process, and ultimately contribute to the growth and development of the technical education sector in India."}
{"id": "SIH1457", "title": "Get Fit Faster with a HighTech Tracker Analyze Human Activity and GPS Data for Improved Results", "category": "Smart Automation", "subcategory": "Software", "organization": "MathWorks India Private Limited", "text": "Title: Get Fit Faster with a HighTech Tracker Analyze Human Activity and GPS Data for Improved Results. Category: Smart Automation. Subcategory: Software. Organization: MathWorks India Private Limited. Description: Description Background, Detailed Description, and Expected Solution Various fields require Human Action Recognition HAR to function optimally. For instance, the healthcare sector needs HAR to monitor the activities of patients, the elderly, or people with specific needs. By doing so, it can offer prompt services and make decisions in realtime according to the requirements. Similarly, the sports industry also utilizes HAR to track a players performance by monitoring their movements, identifying and comparing the actions performed, and conducting automatic statistical analysis. Consequently, it is essential to implement such workflows quickly and more efficiently based on specific domain requirements. An effective solution is to consider various sensor data from mobile phones, such as accelerometers and GPS, and analyze such shortduration time series data. The analysis may aim at estimating the activity, distance traveled, steps taken, calories burned, and altitude climbed, among others. The solution is expected in the form of AndroidiOS app that is developed using Simulink with the help of AndroidiOS support packages. References links below 1. Counting Steps by Capturing Acceleration Data from Your Mobile Device httpsin.mathworks.comhelpmatlabmobileugcountingstepsbycapturingaccelerationdata.html 2. Acquire GPS Data from Your Mobile Device and Plot Your Location and Speed on a Map httpsin.mathworks.comhelpmatlabmobileugacquiregpsdataandplotyourlocationandspeedonamap.html 3. Human Activity Recognition Simulink Model for Smartphone Deployment httpsin.mathworks.comhelpstatshumanactivityrecognitionsimulinkmodelfordeployment.html 4. High School Students Create Fitness Trackers for MATLAB STEM Challenge httpsblogs.mathworks.comstudentlounge20190605fitnesstrackersstemchallenge 5. Introduction to Machine Learning httpsin.mathworks.comvideosseriesintroductiontomachinelearning.html YouTube LinkVideo Link 3minute video explaining the problem statement Signal Processing and Machine Learning Techniques for Sensor Data Analytics  httpswww.youtube.comwatchvGZ3KUPqA1JM"}
{"id": "SIH1456", "title": "Speech to text transcription for Indian languages. The problem entails transcription in the native script and then translation to English. The languages of interest are Hindi, Indian English, Urdu, Bengali, Punjabi.", "category": "Miscellaneous", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Speech to text transcription for Indian languages. The problem entails transcription in the native script and then translation to English. The languages of interest are Hindi, Indian English, Urdu, Bengali, Punjabi.. Category: Miscellaneous. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: The problem entails transcription of audio files to the native script of the audio and then translation to English. The languages of interest are as mentioned below. A key element to be considered is that the solution would be required to be tuned for Indian accents. The languages of interest for the problem statement are shown below \u00c3\u0192\u00c2\u00c3\u00e2\u00c5\u00c3\u00c2 Hindi \u00c3\u0192\u00c2\u00c3\u00e2\u00c5\u00c3\u00c2 Indian English \u00c3\u0192\u00c2\u00c3\u00e2\u00c5\u00c3\u00c2 Urdu \u00c3\u0192\u00c2\u00c3\u00e2\u00c5\u00c3\u00c2 Bengali \u00c3\u0192\u00c2\u00c3\u00e2\u00c5\u00c3\u00c2 Punjabi Datasets pertaining to theses languages will be provided by us, which will consist of two major chunks of data Training Set and a hidden test set. The participants will have only access to the Training set. They will develop their solutions based on the Training set. After the final solution submission by the participants, the final hackathon rankings will be decided by evaluation on the hidden test set. This is done to ensure that the participants solutions generalize better on newer data. The evaluation metric we want to use for this hackathon will be Word Error Rate WER. The WER will be computed between the actual translated text with the solution generated text. The lower the WER the better the model."}
{"id": "SIH1455", "title": "Efficient enumeration of URLs of active hidden servers over anonymous channel TOR", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Efficient enumeration of URLs of active hidden servers over anonymous channel TOR. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: The Onion Routing TOR is an overlay anonymous network over internet, which not only anonymizes clients accessing the TOR network or internet but also facilitate hosting of servers anonymously. These servers have been reported to be hosting various hidden services involved in malicious activities. The goal of this problem statement is to develop Proof of Concept PoC to enumerate URLs .onion of active hidden servers hosted over TOR. Teams are supposed to examine the cryptographic security controls and survey existing vulnerabilities in underlying security architecture of TOR network to develop PoC for efficient enumeration of URLs of active hidden services hosted over TOR."}
{"id": "SIH1454", "title": "Create an intelligent system using AIML to detect phishing domains which imitate look and feel of genuine domains", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Create an intelligent system using AIML to detect phishing domains which imitate look and feel of genuine domains. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: Phishing attack is the most prevalent attack technique to compromise users worldwide. Phishing linkswebsites are shared through number of mediums like email, SMS etc. to target users. These domains are at times host user login page that imitates the genuine target websites. Login attempts on such pages can lead to compromise of user credentials and may also download malicious payload in user computers. The objective of the problem is to identify such phishing domains from the newly registered websites based on open source databases Example WHOIS Database. Such databases provide list of newly registered domains. The tool should be automated and harness power of AIML to identify phishing domains from genuine domains. It may use the following techniques a Backend code  content similarity in web pages. b Web page image analysis i.e. analysis between genuine and phishing site web page images more the similarity better is the probability score of being a lookalike phishing site. The evaluation would be based on the tool\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s ability with regard to the following e Probability scores of phishing domains on how close they are to the genuine domain. f Ability to detect new phishing domains in reasonable time. g Ease of use and flexibility in output formats."}
{"id": "SIH1453", "title": "Investigation of vulnerabilities in implementation of crypto library used by OpenVPN for Internet Protocol Security IPsec, IPV6 deployment.", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Investigation of vulnerabilities in implementation of crypto library used by OpenVPN for Internet Protocol Security IPsec, IPV6 deployment.. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: Internet Protocol Security IPsec is a widely used network layer security control for protecting communications. It is a framework of open standards for ensuring secure communication over IP network. The goal of this problem statement is to identify unknown vulnerabilities in implementation of crypto libraries used by OpenVPN for Internet Protocol Security IPsec, IPV6 deployment. Teams may undertake static dynamic analysis of relevant code to discover any unknown software bug. Emphasis should be on finding unknown vulnerabilities in implementation of cipher suites crypto libraries used by OpenVPN for encryption and authentication in IPsec tunnel. Teams may also investigate and report vulnerable configuration and associated exploitation vector leading to compromise of data confidentiality, which have not been reported so far for IPSEC IPv6 deployment using OpenVPN."}
{"id": "SIH1452", "title": "Develop Ransomware Readiness Assessment tool.", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Develop Ransomware Readiness Assessment tool.. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: Ransomware is a type of malicious software designed to block access ICT devices by encryption of data until ransom is paid to attacker. It is of paramount importance to increase awareness regarding such attacks and assess readiness of the ICT infrastructure of any organisation to thwart these attacks or atleast recover at the earliest. The developer should design and deploy a methodology to evaluate posture and preparedness of an organization towards stopping  mitigating threat from ransomware attack. The developed tool shall be evaluated based on following a Depth of the tool to assess readiness of organization to hinder  stop mitigate ransomware attack. b Assessment of organization towards detection of early signs of ransomware. c Ease of use and awareness imparted by the tool. d Visualization and reporting of the maturity assessment of the organization."}
{"id": "SIH1451", "title": "Develop a AIML tool to detect whether a system  firewall router  network is compromised. The technique should not rely only on IoCs Indicators of Compromises detection.", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Develop a AIML tool to detect whether a system  firewall router  network is compromised. The technique should not rely only on IoCs Indicators of Compromises detection.. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: Early detection of a compromise of any compute device is critical for security of critical information infrastructure. While most of infections on ICT are detected using IoCs Indicators of Compromises, the objective of this problem is to explore techniques for detection of compromise on devices using AI  ML models when the IoC of the compromise is not known. The developer should employ innovative models for nonIoCs based detection of compromise on devices. The evaluation of the solution will be based on the following a Innovation and ruggedness of the method of detection of compromise. b Utility of the method developed over various types of devices including system  firewall  router  network. c Ease of deployment and method of reporting of detected compromise. d Ability to minimize false alarms of compromise."}
{"id": "SIH1450", "title": "Develop and deploy a Large Language Model LLM based tool for generating human like responses to natural language inputs for network not connected over internet", "category": "Smart Automation", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Develop and deploy a Large Language Model LLM based tool for generating human like responses to natural language inputs for network not connected over internet. Category: Smart Automation. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: LLMs have been released by various entities  research organizations for academic and commercial use. These models can be used for generating human like responses on text based systems with serverclient model. The objective of the problem is to develop a tool for network not connected to internet with following basic functions a AIML based Text Summarization of given text. b Summarization of Science andamp Technology SandampT related documents. c Summarization of NEWS papers headlines and editorial pages for quick overview of specific topics. d Reformatting and grammar checks with contextual integrity. e Additional capabilities which the developer can incorporate based on features available in open source LLM models. The solutions shall be graded on the capabilities, ease of use, flexibility and scalability of deployment and number of compatible models."}
{"id": "SIH1449", "title": "Design, develop and implement a software bill of materials SBOM generation tool that can generate the complete SBOM of customdeveloped software including inhouse developments by organisations", "category": "Smart Automation", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Design, develop and implement a software bill of materials SBOM generation tool that can generate the complete SBOM of customdeveloped software including inhouse developments by organisations. Category: Smart Automation. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: SBOM stands for Software Bill of Material and lists out all the packages  modules used from various repositories to make the final solution. This list is essential for identification of vulnerabilities that may impact the final solution. This is critical for supply chain vulnerability management of solutions deployed within the organisation. Thus the task for developers is to develop a software which automatically lists various libraries, dependencies and modules that have been used for making of a given application and generates underlying SBOM. There would be added focus on creating features which can red flag anomalies with an ability to lay out the context to the user. The evaluation shall be based on automation, granularity and accuracy of the SBOM generated. Example, if the developer can identify the version of the libraries used, it shall be graded higher. Ease of use and user experience are other important metrics of evaluation."}
{"id": "SIH1448", "title": "Design of RF UpDownconverter for signals using GNU Radio and SDRs.", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Design of RF UpDownconverter for signals using GNU Radio and SDRs.. Category: Miscellaneous. Subcategory: Hardware. Organization: National Technical Research Organisation,NTRO. Description: Satellite communication happens mainly in high frequency C, Ku band and this needs to be down converted to some Intermediate Frequency say L band which is uniformly accepted. Hence, there is a requirement to Design of RF UpDown converter for from CKu tofrom L band signals using GNU Radio and SDRs. The objective of the problem is to design and implement a tool  mechanism for RF UpDown converter suitable for CKu tofrom L band signals. The developer may use open source signal for design of their tool. Evaluation shall be based on ability of tool to UpDown given signal."}
{"id": "SIH1447", "title": "Identification and Extraction of Forward Error Correction FEC schemes of unknown demodulated signals", "category": "Miscellaneous", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Identification and Extraction of Forward Error Correction FEC schemes of unknown demodulated signals. Category: Miscellaneous. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: FEC is an error correction mechanism with certain trade off in Communication. This is a very critical parameter in order to extract output from a demodulated data. Identification and extraction of Forward Error Correction schemes of unknown satellite signals using MATLABPython is desired for further processing. The objective of the problem is to develop a tool  mechanism for detection  extraction of Forward Error Correction FEC schemes of unknown demodulated signals. The developer may use open source signal for design of their tool. Evaluation shall be based on correct detect  extraction of FEC for given signal."}
{"id": "SIH1446", "title": "Developing a GUI based hardening script for Ubuntu operating system with flexibility to cater for organisational security policies", "category": "Miscellaneous", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Developing a GUI based hardening script for Ubuntu operating system with flexibility to cater for organisational security policies. Category: Miscellaneous. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: Hardening of an operating system involves implementation of security measure to make the system compliant with the security policies of the organization. The procedure for hardening should be intuitive to allow ease of use by personnel with minimal IT skills. The goal of this problem statement is to generate a script which is undertakes hardening of Ubuntu OS using an GUI based approach. During the hardening process, the user should have the flexibility to make settings based on the organisations IT security policy provision like blocking ssh, usb, ToR etc. The grading of tool will be based on hardening functions implemented, attention to user experience and flexibility to take user settings. Developer should remember that security is of utmost importance."}
{"id": "SIH1445", "title": "Deanonymisation for monitoring and tracking of illegal activities performed using cryptocurrency transaction technology", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "National Technical Research Organisation,NTRO", "text": "Title: Deanonymisation for monitoring and tracking of illegal activities performed using cryptocurrency transaction technology. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: National Technical Research Organisation,NTRO. Description: Whatever the darkest corner of diabolical human mind can conceive, DarkWeb can deliver with anonymity and impunity. Dark web markets and forums are filled with illicit activities such as counterfeit currency, fake documents, contraband drugs, ransomware attacks etc. In India, Darkweb crimes have proliferated in recent times especially in the arena of Cyber terrorism, drug trafficking, counterfeit documents, currency and sale of classified Government documents. Governments have also recently raised concern over digital currency and use of DarkWeb for drug trafficking. It is important that appropriate tools and techniques may be developed to monitor and track antinational activities carried out behind the shield of anonymity by using dark web and cryptocurrency technology."}
{"id": "SIH1444", "title": "Students to use Autodesk Fusion 360 to research and develop a design on Smart Glass Cleaning Robot, which can climb, clean, and carry liquid cleaners. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023softwareedition.pdf", "category": "Robotics and Drones", "subcategory": "Software", "organization": "Autodesk", "text": "Title: Students to use Autodesk Fusion 360 to research and develop a design on Smart Glass Cleaning Robot, which can climb, clean, and carry liquid cleaners. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023softwareedition.pdf. Category: Robotics and Drones. Subcategory: Software. Organization: Autodesk. Description: Research and develop a design on Smart Glass Cleaning Robot, which can climb, clean, and carry liquid cleaners. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023softwareedition.pdf"}
{"id": "SIH1443", "title": "Students to use Autodesk Fusion 360 to research and to generate NC code with machine details  tool library for any industrial component. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023hardwareeditioncnc.pdf", "category": "Smart Automation", "subcategory": "Hardware", "organization": "Autodesk", "text": "Title: Students to use Autodesk Fusion 360 to research and to generate NC code with machine details  tool library for any industrial component. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023hardwareeditioncnc.pdf. Category: Smart Automation. Subcategory: Hardware. Organization: Autodesk. Description: Students are supposed to use Fusion 360 Software to generate NC code with machine details  tool library for any industrial component. Students should possess technical skills in areas such as CADCAM software, Gcode programming, toolpath optimization, and machining fundamentals. Additionally, their project ideas should demonstrate a viable solution to a realworld problem, ensuring feasibility and practicality in implementation.. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023hardwareeditioncnc.pdf"}
{"id": "SIH1442", "title": "Students to use Autodesk Fusion 360 to research and redesign a conventional automotive component commonly found in vehicles and utilize generative design to reimagine its design. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023hardwareeditionadditivemanufacturing.pdf", "category": "Smart Vehicles", "subcategory": "Hardware", "organization": "Autodesk", "text": "Title: Students to use Autodesk Fusion 360 to research and redesign a conventional automotive component commonly found in vehicles and utilize generative design to reimagine its design. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023hardwareeditionadditivemanufacturing.pdf. Category: Smart Vehicles. Subcategory: Hardware. Organization: Autodesk. Description: Research and redesign a conventional automotive component commonly found in vehicles and utilize Fusion 360 software to reimagine its design. Students can use Fusion 360 Features such as generative design, Topology Optimization, Additive Build etc. The redesigned component should showcase innovation, enhanced functionality, and improved efficiency, all while being optimized for 3D printing. For additional information and detailed problem statement, please visit httpsdamassets.autodesk.netcontentdamautodeskwwwpdfssih2023hardwareeditionadditivemanufacturing.pdf"}
{"id": "SIH1441", "title": "Similar Document Template Matching Algorithm", "category": "Miscellaneous", "subcategory": "Software", "organization": "Bajaj Finserv Health Ltd.", "text": "Title: Similar Document Template Matching Algorithm. Category: Miscellaneous. Subcategory: Software. Organization: Bajaj Finserv Health Ltd.. Description: In our organization, we encounter a significant challenge when dealing with a large volume of medical invoices, prescriptions, and lab test reports received from numerous providers, including doctors, hospitals, and labs, as well as from customers. To streamline our processes and improve efficiency, we aim to develop a system capable of extracting and standardizing these documents at a provider level. Additionally, we need to identify claims across different providers that exhibit similar document templates. The primary issue we face stems from fraudulent customers who exploit identical digital or printed templates, with minor modifications such as changing the provider names, logos, colors, and text content positioning. By manipulating the customer details within these templates, they attempt to file reimbursement claims that are difficult to detect using standard document comparison checks. To tackle this problem, we require the development of a robust Similar Document Template Matching Algorithm. This algorithm will automate the process of template extraction and standardization, allowing us to identify similarities and patterns across various documents. By comparing the templates used in different claims, we can effectively identify instances of potential fraud, even when the textual content has been altered. The algorithm should be designed to handle a high volume of documents from diverse providers and customers. It should be capable of identifying commonalities in template structure, design elements, and formatting, while also accounting for variations resulting from legitimate differences between providers. Key objectives of the Similar Document Template Matching Algorithm 1. Template Extraction Develop a mechanism to extract and standardize templates from medical invoices, prescriptions, and lab test reports received from providers and customers. 2. Template Comparison Implement an algorithm to compare and identify similarities among different document templates, accounting for minor modifications and variations in content. 3. Fraud Detection Enhance the algorithm to flag instances of potentially fraudulent claims where similar templates are used, with modifications made to customer details while maintaining the overall structure and design. 4. Scalability and Efficiency Ensure the algorithm can handle a large volume of documents efficiently, considering the growing number of providers and customers we deal with. 5. Flexibility Design the algorithm to accommodate variations in template design and content across different providers, while maintaining the ability to detect fraudulent patterns The successful development and implementation of the Similar Document Template Matching Algorithm will significantly enhance our ability to detect fraudulent claims and improve the overall accuracy and efficiency of our document processing workflows. Theme Fraud Detection  Document Templatization Evaluation Criteria The effectiveness of the Similar Document Template Matching Algorithm will be evaluated based on several key criteria. The primary criterion is the accuracy of detection, which measures the algorithms ability to identify similarities in document templates accurately and efficiently. The algorithm should be able to detect instances where fraudulent customers exploit identical templates with minor modifications, distinguishing between suspicious and fraudulent documents. To aid in the interpretation and communication of the algorithms findings, the detected output should be represented using colorcoded flags. Amber flags can be used to indicate suspicious documents that exhibit similarities in template structure or design elements, while red flags should be assigned to identify documents with high potential for fraud. This visual representation will assist investigators in prioritizing their efforts and taking appropriate actions. Input Data Set The team will have to generate their own dataset using publicly available datasets or archives. This approach ensures that the algorithm is exposed to a wide range of document templates from various providers and customers. The dataset should encompass different document types, such as medical invoices, prescriptions, and lab test reports, reflecting the realworld scenarios the algorithm will encounter during deployment. By using this selfgenerated dataset, the algorithm can be trained and evaluated under realistic conditions. This approach enables the algorithm to learn patterns and similarities that exist in actual documents, allowing it to accurately detect fraudulent claims and identify suspicious patterns across different providers and customers. Teams working on this problem statement are advised to create their own dataset to build and train their algorithms."}
{"id": "SIH1440", "title": "An application under which all rescue agencies are registered and which can display the location of other rescue relief agencies during natural man made calamities", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: An application under which all rescue agencies are registered and which can display the location of other rescue relief agencies during natural man made calamities. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Home Affairs. Description: To build such an application. there is need to create a central database where all rescue agencies can register their information, including their location, contact details, and areas of expertise. This information could be entered manually by agency administrators, or automated using GPS or other location tracking technologies. Once the database is populated, the application would need to be designed to display this information in an easytouse interface It could include a map that shows the locations of all registered rescue agencies, along with filters that allow users to narrow down the results based on specific criteria, such as the type of disaster, the resources available, or the time since the last reported activity. In addition to displaying the locations of rescue agencies, the application could also include features for communication and collaboration. For example, agencies could send alerts or requests for assistance to each other directly through the application, or collaborate on shared resources such as medical equipment or transportation. Security and privacy would be major considerations in building such an application. It would be important to ensure that only authorized users have access to the database, and that sensitive information such as personal contact details is protected. Overall, building an application that allows rescue agencies to coordinate their efforts and provide aid more effectively could be a valuable tool for responding to natural or manmade disasters."}
{"id": "SIH1439", "title": "Robotics Device for Borewell Rescue Operation", "category": "Disaster Management", "subcategory": "Hardware", "organization": "Ministry of Home Affairs", "text": "Title: Robotics Device for Borewell Rescue Operation. Category: Disaster Management. Subcategory: Hardware. Organization: Ministry of Home Affairs. Description: A robotics device for borewell rescue operation for NDRF can be a valuable tool to assist in the rescue of individuals who are trapped in a borewell. These devices can be equipped with cameras, sensors, and other tools that can be used to locate and extract individuals who are stuck in a borewell. In recent years, there have been several incidents in India where children and adults have fallen into open borewells, resulting in tragic outcomes. The rescue of individuals who are trapped in borewells is a complex and challenging operation that requires specialized equipment and expertise. NDRF is often called upon to assist in these rescue operations. A robotics device for borewell rescue operation can significantly enhance NDRFs capabilities in these situations. The device can be equipped with cameras and sensors that can provide realtime data on the location and condition of the individual trapped in the borewell. It can also be equipped with specialized tools that can be used to extract the individual safely The robotics device can be remotely controlled, which means that it can be operated from a safe distance. This feature can be particularly useful in situations where the borewell is unstable, and there is a risk of further collapse. The device can also be designed to be lightweight and compact. making it easy to transport to the rescue site. The deployment of such robotics device for borewell rescue operations can be a significant step in enhancing NDRFs capabilities in these complex and challenging situations. It can help to save lives and minimize damage during these emergencies."}
{"id": "SIH1438", "title": "Virtual RealityBased Training for CBRN Disaster Response", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: Virtual RealityBased Training for CBRN Disaster Response. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Home Affairs. Description: A virtual realitybased training for CBRN Chemical, Biological, Radiological, and Nuclear disaster response for NDRF is a training program that may uses virtual reality technology to simulate various CBRN emergency scenarios. It may designed to train NDRF personnel to respond effectively to such emergencies, as currently there is no system available to train them in this regard. Virtual realitybased training immerses trainees in realistic scenarios that simulate the effects of CBRN emergencies. The technology allows trainees to experience the situations as if they were real without exposing them to actual risks. Through the use of specialized software and equipment, trainees can interact with virtual objects, practice rescue techniques. and learn to handle hazardous materials and situations. This type of training can be particularly useful for NDRF rescuers who may face hazardous and highrisk situations during CBRN emergencies. It can help them to gain the necessary skills. knowledge, and confidence to manage such emergencies effectively, which can ultimately save lives and minimize damage. Overall, the development of a virtual realitybased training program for CBRN disaster response for NDRF may be a necessary step to improve the training and preparedness of NDRF personnel for CBRN emergencies."}
{"id": "SIH1437", "title": "Development of suitable Software and Hardware componentssensors for springshed mapping, monitoring and management.", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Software", "organization": "Ministry of Rural Development", "text": "Title: Development of suitable Software and Hardware componentssensors for springshed mapping, monitoring and management.. Category: Agriculture, FoodTech  Rural Development. Subcategory: Software. Organization: Ministry of Rural Development. Description: Main objective is to develop suitable software and hardware components for a comprehensive GISbased mapping system to help visualize the springshed boundaries, water sources and other related data. Predictive model to forecast the water availability in the springshed for domestic and other use. Workable data management system to store and analyze data related to the springshed. A good decisionsupport system model to guide and help stakeholders make informed decisions related to springshed management. An automated alert system to inform stakeholders of changes in the springshed landscapes. Methodology Hardware 1. Highresolution airborne LiDAR systems This can be used to accurately measure the topography of springsheds, including surface elevations, contour lines and watershed boundaries.2. Water quality sensors These sensors measure various water quality parameters such as temperature, pH, dissolved oxygen, salinity, nutrients and other contaminants. 3. Stream flow gauges Which measure the volume of water flowing through a nala\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s, streams or rivers. 4. Weather stations measure the parameters such as temperature, humidity, wind speed, precipitation and solar radiation. Software 1. GIS software GIS software can be used to map and analyze the topography of springsheds. 2. Hydrogeological modeling software This software can be used to simulate the dynamics of springsheds, such as the flow of water and sediment through the system. 3. Data management software Data management software can be used to store, organize and analyze the data collected from hardware sensors. 4. Decision support software Decision support software can be used to analyze the data collected from hardware sensors and generates recommendations for springshed management."}
{"id": "SIH1436", "title": "Number of persons benefitted from using the Water Harvesting Structures WHS created  rejuvenated in the project areas under WDC 2.0 for economic activities like fisheries etc for alternate source of income generation.", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Software", "organization": "Ministry of Rural Development", "text": "Title: Number of persons benefitted from using the Water Harvesting Structures WHS created  rejuvenated in the project areas under WDC 2.0 for economic activities like fisheries etc for alternate source of income generation.. Category: Agriculture, FoodTech  Rural Development. Subcategory: Software. Organization: Ministry of Rural Development. Description: Creation  Rejuvenation of WHSs are one of the major activities under WDCPMKSY. Besides being a source of protective irrigation in the rainfed areas, contributing towards improvement in soil moisture, increase in water table etc, the WHSs are also utilized for livelihood activities like fisheries, aquatic vegetable cultivation etc providing substantial incomes to the project community. Methodology Computing of Incomes generated by the families community directly involved in use of the WHSs for their income generation activities. Organization Department of land Resources Ministry of Rural Development Category Software Domain Bucket Livelihood  Income generation"}
{"id": "SIH1435", "title": "Developing an algotithim  programme to measure increase in income of FPOs on account of project interventions under WDC 2.0.", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Software", "organization": "Ministry of Rural Development", "text": "Title: Developing an algotithim  programme to measure increase in income of FPOs on account of project interventions under WDC 2.0.. Category: Agriculture, FoodTech  Rural Development. Subcategory: Software. Organization: Ministry of Rural Development. Description: With the objective to help achieve higher economic growth for the project community and collectivization of farm operations, which can be realized through FPOs, the New Generation Watershed Guidelines envisage formation of FPOs right from the planning stage of the projects. The FPO shall be the member  owned and member  managed institution. Any household dependent directly or indirectly on the natural resources of watershed can join the FPO by paying prescribed share capital amount and membership fee as defined by the organization. Methodology Computing of Incomes generated through provision of demand based services, operation of Custom Hiring Centre CHC, marketing of local agricultural produce through alternate market channels etc."}
{"id": "SIH1434", "title": "Making career choices and AI based counselling accessible to every child at secondary level along with aptitude tests and detailed career paths.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Education", "text": "Title: Making career choices and AI based counselling accessible to every child at secondary level along with aptitude tests and detailed career paths.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Education. Description: The project team shall establish an interactive AI based model that will help students to to choose from careers. The model should handhold student in assessing his capabilities and subsequently help him in deciding a career path."}
{"id": "SIH1433", "title": "Mental health and wellbeing surveillance, assessment and tracking solution among children.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Education", "text": "Title: Mental health and wellbeing surveillance, assessment and tracking solution among children.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Education. Description: Develop a modelsoftware which will help students to assess mental health of students, build methods to find out and provide solution for the improvement"}
{"id": "SIH1432", "title": "Identify slow learners for remedial teaching and capacity building for innovative methods.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Education", "text": "Title: Identify slow learners for remedial teaching and capacity building for innovative methods.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Education. Description: Developed based on available assessment data for students the slow learners and build inovative methods to help them cope up with the educational requirements"}
{"id": "SIH1431", "title": "Online personalized learning remediationtutoring tool. Search for best teacher for specific topics.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Education", "text": "Title: Online personalized learning remediationtutoring tool. Search for best teacher for specific topics.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Education. Description: Develop a modelsoftware which will help students to select best teacherinformation about specific topic and help them in personalised learning"}
{"id": "SIH1430", "title": "Lowest Cost Smart Board  A Seamless Teaching Experience.", "category": "Smart Education", "subcategory": "Hardware", "organization": "Ministry of Education", "text": "Title: Lowest Cost Smart Board  A Seamless Teaching Experience.. Category: Smart Education. Subcategory: Hardware. Organization: Ministry of Education. Description: Develop a smart board for students which will help students in effective interaction and teaching experience"}
{"id": "SIH1429", "title": "Develop a solution to identifo discrepancies in Quality Monitoring Data on OMMAS and generate reports that highlight areas where corrective action is Required.", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Rural Development", "text": "Title: Develop a solution to identifo discrepancies in Quality Monitoring Data on OMMAS and generate reports that highlight areas where corrective action is Required.. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Rural Development. Description: The task is to develop a solution that leverages the quality monitoring data available on OMMAS to identify discrepancies between various data sources, The data is collected through an Eform report and test datasheet filled by second and thirdtier quality monitors QMs and a quality control register QCR scanned copy uploaded by State Project lmplementation Units PlUl. 1. The QM report which has been digitalized and called an Efrom we currently get from OMMAS is filled in by Quality Monitors QMs. Ihe report contains data on all road layers and other pertinent information, such as geometry road furniture, drains, etc. The report includes values of important parameters from DPR, values recorded at the site by second and thirdtier quality monitors, YesNo answers, and values from QCR. 2. The test reportdatasheet contains values recorded at the site for every test. Test results need to be recorded in the QM report Eform for each test. This report is cunently uploaded by QMs as a scanned pdf. lt will be digitized in a few days, and we will receive the inspection data in digitized format only. 3. Qualfi Control Registers QCR are the record of the tests conducted at the site by the Contractor engineer and the authority engineer JE, AE and EE. eCn has two parts. eCR partr needs to be filled and maintained by the contractor, and part_ll needs io be filled bv the authority project imprementation unit pru. prUs currentry ,pu.J ecn, ., scanned pdf copies. The solution should be able to compare the data from these sources with the standard values for each test and analyse the three sources separately. The aim is to identifo discrepancies in the data and enable effective quality monitoring of the processes involved. The solution should be able to process large volumes of data and provide actionable insights that can be used to improve the quality of the processes being monitored. Probable Outputs 1. Develop a machine learning algorithmtool that can analyze the quality monitoring data on OMMAS and identifo discrepancies between the different sources of data. The algorithm can be trained on historical data to identif patterns and use thess patterns to predict discrepancies in realtime. The output of the algorithm can be used to generate reports that highlight areas where corrective action is required. 2. Build a dashboard that visualizes the quality monitoring data on OMMAS and enables users to compare the data from different sources. The dashboard can be customized to display data in various formaG, such as tables, charts, and graphs. The users can interact with the dashboard to drill down into specific data points and identifo discrepancies between the different sources of data. 3. Develop a data processing pipeline that automates the process of analyzing the quality monitorin8 data on OMMAS. The pipeline can be designed to intest data from different sources, such as the QM report Eform, test reports, and QCRs. The pipeline can thenprocess the data using various tools and techniques, such as data cleaning transformation, and analysis. The output of the pipeline can be used to generate reports that highlight areas where corrective action is required. 4. Use natural language processing NLP techniques to analyze the quality monitoring data on OMMAS and identifo discrepancies between the different sources of data. NLP techniques can be used to extract relevant information from the QM report Eform, test reports, and QCRs, and compare this information to identifo discrepancies. The output of the NLP analysis can be used to generate reports that highlight areas where corective action is required"}
{"id": "SIH1428", "title": "ARVR based application which helps visualize the complete house virtually at an early stage, which can help in cost estimation, planning and completion of the house in time", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Rural Development", "text": "Title: ARVR based application which helps visualize the complete house virtually at an early stage, which can help in cost estimation, planning and completion of the house in time. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Rural Development. Description: ARVR technology based solution will help the beneficary to get a virtual miniature of the completed house at an early stage and will also give an estimate of the cost so that the beneficiary can do the necessary planning before starting the construction."}
{"id": "SIH1427", "title": "Call for Toilet technology", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Jal Shakti", "text": "Title: Call for Toilet technology. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Jal Shakti. Description: Swachh Bharat Mission Grameen promotes affordable, sustainable with low OandampM and environment friendly technologies. However, our country being vast with quite a variation in the geographical and biogeographical features the variation in water table, dryness and aridness or rains is observed annually. This is especially true for areas which receive high rains with low percolation in soil, rocky hills, fragile hillocks prone to landslides, arid and areas with poor connectivity and infrastructural setup.andquot Solutions invited for innovative toilet technologies from the participants for the following areas \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Hard rocky areas \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Fragile hill areas prone to land slides \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Flood prone and low lying moist areas \u00c3\u0192\u00e2\u0161\u00c3\u00c2 River bed, wetlands and coastal areas \u00c3\u0192\u00e2\u0161\u00c3\u00c2 High rainfall areas \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Containment of and onsite treatment of faecal sludge in septic tanks in such areas without the seepage to avoid ground water contamination \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Specific local solutions by any innovation Any solutions for the upgradation of single pit to the twin pit system are also welcome. It was expected that the technology should be costeffective, sustainable, reliable and durable, user friendly, weather proof, environmentalfriendly, and preferably uses locally available material locally implies the area to which the technology is built for."}
{"id": "SIH1426", "title": "Technological solutions for safe disposal of menstrual waste", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Jal Shakti", "text": "Title: Technological solutions for safe disposal of menstrual waste. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Jal Shakti. Description: With increase in education and awareness levels around Menstrual Health and Hygiene, more and more women and adolescent girls in the country are switching to safe sanitary options to manage their menstrual cycles. However, there is still no formal waste management system of sanitary waste. Often these are disposed off in fields, water bodies, flushed in toilets or dumped along with the regular solid waste. Solutions to manage and dispose of sanitary waste. The solutions should be \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Safe for the environment, and not cause any air, water or soil pollution \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Costeffective \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Scalable across villages and institutions such as schools, colleges, etc."}
{"id": "SIH1425", "title": "Technological solutions for Early decomposition of fecal matter", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Jal Shakti", "text": "Title: Technological solutions for Early decomposition of fecal matter. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Jal Shakti. Description: In large parts of rural India, onsite treatment is preferable over sewer system owing to the ease of implementation and cost effectiveness. Any solution which helps decompose faecal matter quickly will enable easy and safe emptying of the toilet pitseptic tank. This will reduce the turnaround time for reuse of the pitseptic tank and will lead to sustained use of the toilets. Solutions to expedite the process of decomposition of faecal material. It was expected that the technology should \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Decompose the faecal material in the shortest possible time \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Costeffective \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Scalable \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Easy to implement \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Safe treatment \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Manure \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Weather proof and environmentalfriendly"}
{"id": "SIH1424", "title": "Call for lowcost desalination technology for Lakshadweep and Inland saline water sources.", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Jal Shakti", "text": "Title: Call for lowcost desalination technology for Lakshadweep and Inland saline water sources.. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Jal Shakti. Description: Desalination of seawater and brackish water will play an important role to meet the drinking water for Lakshadweep. A cost effective technology for desalination for Lakshadweep and inland saline water is required to meet the water supply demand at every rural Households. The technology should focus on  \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Higher fresh water recovery. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Quality water supply as per BIS 10500. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Low energy cost. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Preventive scaling measures. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Low Operation and maintenance."}
{"id": "SIH1423", "title": "Web Mobile based tool for mapping of Water supply network", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Jal Shakti", "text": "Title: Web Mobile based tool for mapping of Water supply network. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Jal Shakti. Description: Jal Jeevan Mission was launched with the vision of providing drinking water in adequate quantity of prescribed quality on regular and longterm basis to every rural household. A cost effective technology is required for providing web mobile based tool for mapping of water supply network through creating geospatial database of all major structure of Water supply system with provision with grievance redrassal and IoT system for alert monitoring."}
{"id": "SIH1422", "title": "Devise the method for identification of victims buried under avalanches.", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: Devise the method for identification of victims buried under avalanches.. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Defence. Description: Description Background Avalanches can be very dangerous and unpredictable. Even if we take preventive measures to avoid them, probability of getting caught in an avalanche is still there. Ff victims are buried under avalanche, it is important to take immediate action because the probability of survival decreases with time survival chances goes down to less than 30 after an hour. Avalanche victim detector or other Ground Penetrating radarbased methods are there for victim identification but these field based techniques are difficult to use during harsh climatic condition but these field based techniques are difficult to use during harsh climatic conditions and rough terrain. Explore the new technologies for example in the field of AlML that can be used for detection of victim buried under an avalanche."}
{"id": "SIH1421", "title": "Novel Technologies for Early Detection and Mitigation of Avalanches", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: Novel Technologies for Early Detection and Mitigation of Avalanches. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Defence. Description: Avalanche is a sudden downhill movement of snow mass along with rocks and boulders which can cause damage to human life and property. An early detection of avalanches and prewarning about the threat can save precious lives and habitats. Suggest innovative ideas for early prediction and Mitigation of Snow Avalanches in vulnerable mountain terrain."}
{"id": "SIH1420", "title": "Lean module for reasoning about computational complexity in GPTs.", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: Lean module for reasoning about computational complexity in GPTs.. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Defence. Description: Formalization of mathematics and computer science is in vogue. Formalization means to express mathematical concepts, definitions, theorems, and proofs in a way that can be checked by a computer for correctness. Lean theorem prover is interactive and based on dependent type theory which is a powerful and expressive framework for formalizing computer science. It has been used for example in formalization of number theory. Similarly, it can be used to formalize the notion of computational complexity of generalized probabilistic theories GPTs. Some of the relevant computational complexity classes are BGP AWPP, BQP, BPP, PP, PSPACE. We can formalize the notions of these computational complexity classes and the relations among them in lean. We can also move further on to formalize the notion of higher order interference in physical theories and formalize some theorems related to it. The exact problem is to write relevant lean modules containing the basic definitions, results in GPTs and tools for reasoning about computational complexity in GPTs. The developed module can further be used to give formal proofs for theorems and lemmas."}
{"id": "SIH1419", "title": "Robust human target detection and acquisition", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: Robust human target detection and acquisition. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Defence. Description: Robust human target detection and acquisitionlocking without losing track under occlusions for outdoor operational scenarios. Visuals alarm generation upon anomaly detection i.e human jumping crawling running."}
{"id": "SIH1418", "title": "Deep learning for terrain recognition", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: Deep learning for terrain recognition. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Defence. Description: Vision based methods using deep learning such as CNN to perform terrain recognition sandyrockygrassmarshy enhanced with implicit quantities information such as the roughness, slipperiness, an important aspect for highlevel environment perception."}
{"id": "SIH1417", "title": "AIML based intelligent desmokingdehazing algorithm", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: AIML based intelligent desmokingdehazing algorithm. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Defence. Description: Design and Development of AIML based intelligent de smokingdehazing algorithm for reproducing the real time video of the area under fire specifically for indoor fire hazards to aid the rescue operation."}
{"id": "SIH1416", "title": "AI based Automatic alarm generation and dropping of payload at a particular object through a Drone.", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: AI based Automatic alarm generation and dropping of payload at a particular object through a Drone.. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Defence. Description: Drones are currently being widely utilized in civilian applications and military uses too. In severe earthquake and flood situations, the Drones with artificial intelligence based automatic object recognition capability can help rescue teams a lot through automatic alarm generation and dropping of payloads like food, clothes, rescue tools near detected human being. Challenge here is to detect human being from around 50100 m above the ground at a slanted angle and then drop a payload say flower for demonstration purposes near him her. Technologies needed will be Drone, Cameras, Processor Board Raspberry Pi or Intel NUC, Artificial Neural Network or AI hardware and Software, Payload dropping mechanism etc. The developed system will be highly useful in many DRDO projects and civilian applications."}
{"id": "SIH1415", "title": "Development of motion amplification video techniques for vibration analysis", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Defence", "text": "Title: Development of motion amplification video techniques for vibration analysis. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Defence. Description: Motion Amplification video MAV is a technique for visualizing and measuring vibration of structures and machinery. This processes a video clip of an object, extracts feature that are moving from frame to frame, then amplifies and replays the motion in each frame. Defects at micro scales are rendered visible. Vibration amplitudes and mode shape can be thereafter be determined. Time waveform and FFT spectrum can also be captured. This would be extremely useful in evaluating noise, vibration and shock on various platforms. It is also useful in automobile, power plants, industry and other engineering sectors."}
{"id": "SIH1414", "title": "Drone based Intelligent Magnetic sensing system and Metallic anomaly detection system", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Defence", "text": "Title: Drone based Intelligent Magnetic sensing system and Metallic anomaly detection system. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Defence. Description: Most of the metallic objects are constructed of Ferromagnetic materials and in the presence of Earth\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s magnetic field, these create localized magnetic disturbances in Earth magnetic field which is termed as Magnetic signature. This magnetic signature of objects can be used for detection, identification and classification of objects. The above problem statement envisages, that a Drone based Intelligent Magnetic sensing system be developed to assess the magnetic fields in a particular area above sea or above land and identify any magnetic anomalies in that measurement region. A Portable Drone Control module for control along with an inbuilt learning and prediction algorithms to compensate self magnetic field of drone and measure the magnetic fields in a particular area of interest also be developed. Suitable metallic object detection methodology for identifying unidentified metallic magnetic anomaly in that particular region is also to be developed. A drone fitted with magnetic sensing system be developed with suitable compensation methodology for compensating own drone\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s magnetic fields. Based on open source geomagnetic anomalies data of a particular region, identification and classification of metallic magnetic anomaly to be carried out."}
{"id": "SIH1413", "title": "Dak Ghar Niryat Kendra", "category": "Transportation  Logistics", "subcategory": "Software", "organization": "Ministry of Communications", "text": "Title: Dak Ghar Niryat Kendra. Category: Transportation  Logistics. Subcategory: Software. Organization: Ministry of Communications. Description: Dak Ghar Niryat Kendra DNK is an initiative of the Department of Posts and CBIC, wherein the small exporters are able to electronically file a Portal Bill of Export and then present the parcel at DNK for export. 122 DNKs have so far been opened in Post Offices and 878 more shall be opened in the next two years. Although DNKs are being used by 800 exporters, DoP is still not able to bring small traders, artisans and MSMEs onto the DNK platform as most of such entrepreneurs do not have the wherewithal to create web portals for displaying their products and accepting International eCommerce orders, or they are find the regulatory and eCommerce platform compliances too expensive or tedious. Moreover, the international customers find it difficult to trust the webportals or small entities. The private eCommerce portals charge upto 40 of the sale price of orders, which is prohibitively expensive. Dak Ghar Niryat Kendra refers to a concept in the Indian postal system. It translates to Post Office Export Centre in English. The Dak Ghar Niryat Kendra is a specialized facility within a post office that offers services related to export of goods.The establishment of Dak Ghar Niryat Kendra aims to simplify and streamline the export process for small and mediumsized enterprises SMEs by leveraging the extensive postal network and infrastructure already in place. It provides exporters with a costeffective and accessible solution for their export logistics requirements."}
{"id": "SIH1412", "title": "Data Compression for backbone network", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: Data Compression for backbone network. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Ministry of Home Affairs. Description: Developing a data compression system for a backbone network. Proposed Solutionyou can develop a data compression system for a backbone network that efficiently compresses data, reduces network bandwidth requirements, and optimizes network performance while considering the specific requirements and constraints of the network environment."}
{"id": "SIH1411", "title": "Simulator system for counter Hijack and sky Marshalling operations", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: Simulator system for counter Hijack and sky Marshalling operations. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Ministry of Home Affairs. Description: Developing a simulator system for counter hijack and sky marshalling operations.You can develop a simulator system that provides realistic and effective training for counter hijack and sky marshalling operations. Collaborating with experts in aviation security, law enforcement, and simulation technology can further enhance the development process and ensure the system meets the specific needs of the training program."}
{"id": "SIH1410", "title": "Augmented Virtual reality system for the live training of troops", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: Augmented Virtual reality system for the live training of troops. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Home Affairs. Description: Develop an Augmented Reality AR or Virtual Reality VR system for the live training of troops.ARVR system for live training of troops requires expertise in hardware, software development, content creation, and user experience design. Collaboration with domain experts, instructional designers, and technology specialists can help ensure the system meets the specific needs of the military training environment."}
{"id": "SIH1409", "title": "AI based IT training system", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: AI based IT training system. Category: Smart Education. Subcategory: Software. Organization: Ministry of Home Affairs. Description: Modern technology is changing rapidly and every individual has different level of understanding and different pace of learning things. Having same course for all personnel not only result in waste of time of departments and personnels but also affects departments productivity. Proposed Solution AI Based training system may be developed to real time design the course based on individual understanding and learning capacity."}
{"id": "SIH1408", "title": "IT System log analyzer", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: IT System log analyzer. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Ministry of Home Affairs. Description: CRPF unitsoffices and personnel are deployed in different location of CRPF. There is no centralised system to analyse the log of IT system by the experts to access threats and breaches. Proposed Solution Centralized system should be developed for analysing the systems deployed at the different location of the country Expert per problems statement"}
{"id": "SIH1407", "title": "Chatbot based helpdesk for govt employees and departments", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Home Affairs", "text": "Title: Chatbot based helpdesk for govt employees and departments. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Home Affairs. Description: Govt. employees and departments need help in various process of general business nature ranging from procurement to implementation, authority to directions, etc. Proposed Solution Centralised and department wise Chatbot based helpdesk should be developed Benefits 1. Increase productivity 2. Increase inters department coordination 3. Increase job satisfaction among Govt. employees directly and indirectly in citizens."}
{"id": "SIH1406", "title": "Development of Augmentative and Alternative Communication AAC in Indian context", "category": "MedTech  BioTech  HealthTech", "subcategory": "Hardware", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Development of Augmentative and Alternative Communication AAC in Indian context. Category: MedTech  BioTech  HealthTech. Subcategory: Hardware. Organization: Ministry of Social Justice and Empowerment. Description: communication is very important for exchanging the need, Idea and feeling among others. ACC is an option for nonverbal mode of communication for the persons with communication deficit. This mode is especially used by non verbal individuals. Those who are having multiple disability and communication deficit, they must have to communicate with others but due to lack to motor coordination, intellectual deficit and sensory perceptual deficit not able to communicate with others. This is the major problem related of communication for the multiple disability or mainly those were having maximum deficit in motor coordination for communication, Due to communication deficit peoples are not able to communicate with the society. HITECH AAC is available in India which is developed in Foreign Countries that is very costly. There is need to develop an electronically low cost AAC and hardware made in Indian context. If AAC electronic device and hardware mode will be develop in Indian context, it will be easy and helpful for persons with nonverbal communication deficit. If it is developed low cost effective as per Indian context then it will be benefits the most of the population from AAC."}
{"id": "SIH1405", "title": "Develop therapy materials in Hindi for misarticulation children", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Develop therapy materials in Hindi for misarticulation children. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of Social Justice and Empowerment. Description: Many treatments are available to treat misarticulation, speech therapy or articulation therapy focuses on pronunciation and talking. It deals with a persons ability to move the lips, tongue, teeth, and jaw to produce speech sounds. Misarticulation therapy is one of the most effective treatments for the articulation problems but all are treatments are available in English for that we need materials or treatments in Hindi version that will help individual to treat articulation problem in our Indian context. We need software in which there are number of therapy activities and techniques should be involve in which 3 to 4 domain will be included that is 1. Position level in which position of sound will be shown 2.Phoneme level Initial, medial and final level in which all the words should be there in all three level 3.Picture level in which word related picture should be present and last included number of correct and incorrect word individual speak, will be shown it will help individual to determine their incorrect word production. So if this kind of app should be prepare in Hindi version it will very helpful for the professional along with children and their parents. I will share some link which is in English version."}
{"id": "SIH1404", "title": "Lowcost Myoelectric Prosthetic Arm", "category": "MedTech  BioTech  HealthTech", "subcategory": "Hardware", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Lowcost Myoelectric Prosthetic Arm. Category: MedTech  BioTech  HealthTech. Subcategory: Hardware. Organization: Ministry of Social Justice and Empowerment. Description: In India the cost of a new MyoelectricBionic prosthetic arm can range from 1lakh  2 lakh Rupees, putting the Myoelectric hand out of reach of many middleclass and lower middleclass patientsamputee, we decided to design and build a far cheaper and pocket friendly version of prosthetic arm with Myoelectric capabilities. We are using 3DPrinting technology for the structure of the prosthetic hand, and servo motors and Arduino for the mechanical purpose, Myoware muscle sensor is responsible for reading the electrical signals generated by muscle. These components and microprocessor can bring the cost of the prosthetic arm much cheaper and pocket friendly for the patientamputee."}
{"id": "SIH1403", "title": "APP Based Digital Audiometer", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: APP Based Digital Audiometer. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of Social Justice and Empowerment. Description: To know a person Hearing Loss, generally we use a Pure Tone Audiometer. To use this it requires a bulk of the instrument to be carried. This testing requires a considerable amount of time for testing. It was observed that during a large level hearing testing it is consuming much time. So a hand held Mobile phone operated APP BASED AUDIOMETER will be of immense useful in finding and detecting the hearing loss in children in a schools"}
{"id": "SIH1402", "title": "Ear Electronic Device for Tinnitus Frequency Finding and Adjusting to Provide Relief to Tinnitus Suffering Patient", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Ear Electronic Device for Tinnitus Frequency Finding and Adjusting to Provide Relief to Tinnitus Suffering Patient. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of Social Justice and Empowerment. Description: Various studies have shown that when a person suffers with TINNITUS NOISE he suffers due to anxiety or stress, as these stimulate an already sensitive hearing system which effects his day to day work performing, which has an impact on his livelihood and family. One of the effective treatment consists of using an electronic device to suppress the tinnitus noise. Which finding the frequency of the tinnitus. noise and suppressing requires a lot of instrumental support. Thus if t can be possible that by observing and analysing the tinnitus noise by the electronic device in the ear, the device itself finds the tinnitus noise frequency and suppress it accordingly, bringing benefit to the patient suffering from tinnitus."}
{"id": "SIH1394", "title": "Virtual Zoo", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Environment", "text": "Title: Virtual Zoo. Category: Smart Education. Subcategory: Software. Organization: Ministry of Environment. Description: Design software and hardware to display holograms of existing and extinct species of flora and fauna to depict complex ecosystems from various parts of the world. The virtual zoo could be displayed in schools, museums, and Ministries without using much physical space. The zoo could also hold educational sessions about the environment within the virtual space using prerecorded videos of teachers from around the world."}
{"id": "SIH1393", "title": "Water and Electricity Tracking App", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Environment", "text": "Title: Water and Electricity Tracking App. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Environment. Description: A mobile application that helps you limit your water and electricity usage to a predetermined goal by outlining the behavioural change that would be required to meet those targets. Behavioural nudges ought to be embedded in the user experience based on deep research about the best practices of efficient water and electricity usage from around the world."}
{"id": "SIH1392", "title": "EWaste Facility Locator", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Environment", "text": "Title: EWaste Facility Locator. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Environment. Description: Website that tells you the location of the nearest ewaste collection and recycling facility. Offers educational popups on the harmful components of your ewaste and their effects on the environment and human health if not disposed correctly. There could be an option to input the model of your old device and earn credit points relative to the amount of precious metals recovered from the device if disposed correctly."}
{"id": "SIH1391", "title": "Behavioural change monitoring software", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Environment", "text": "Title: Behavioural change monitoring software. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Environment. Description: A software that allows the ministry to monitor the success of their behavioural nudges in real time with respect to consumer choices and environmentally friendly actions. The software will define certain measurable outcome indicators and track the changes in them over time. The changes will be correlated to LiFE activities and awareness programmes to measure the success of the behavioural nudges."}
{"id": "SIH1390", "title": "Pro Planet Person App", "category": "Clean  Green Technology", "subcategory": "Software", "organization": "Ministry of Environment", "text": "Title: Pro Planet Person App. Category: Clean  Green Technology. Subcategory: Software. Organization: Ministry of Environment. Description: A mobile application that tracks user\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s activities and actions and tells the user if she is Pro Planet. Also offers suggestions on how to become a Pro Planet Person. Can use popups and fun facts to nudge behaviors. The app can track behavior\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s across other selected apps such as food delivery, online ordering, etc. to nudge the users to choose sustainable alternatives in real time. For example, while ordering food, the app will send a popup reminding the user to deselect the option of disposable cutlery."}
{"id": "SIH1389", "title": "To develop centralised information security .Logcollection facility or security operation centre soc in the power sector, considering cEA cybersecurity Power sector Guidelines, 2021to keep lr and or networking System isolated and airgapped.", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Ministry of power", "text": "Title: To develop centralised information security .Logcollection facility or security operation centre soc in the power sector, considering cEA cybersecurity Power sector Guidelines, 2021to keep lr and or networking System isolated and airgapped.. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Ministry of power. Description: Cyber intrusion attempts and cyberattacks in any critical sector are carried out with a malicious intent. ln Power sector, its either to compromise the power Supply system or to render the grid operation insecure. Any such compromise may result in maloperation of equipment\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s, equipment damages or even in a cascading grid brownoutblackout. The muchhyped air gap myth between lr and or systems now stands shattered. The artificial air gap created by deploying firewalls between any lr and or system can be jumped by an insider or an outsider through social engineering. Cyberattacks are staged through tactics and techniques of lnitial Access, Execution, persistence, privilege Escalation, Defense Evasion, Command and Control, Exfiltration. After gaining an entry inside the system through privilege escalation, the control of lr network and operations of or systems can be taken over even remotely by any cyber adversary. The gain of sensitive operational data through such intrusions may help the NationState sponsored or nonsponsored adversaries and cyberattackers to design more sinister and advanced cyberattacks. How to develop centralized information security logcollection facility or Security Operation Center SoC in the Power Sector, considering cEA cybersecurity power Sector Guidelines 2021, to keep lT and OT networking System isolated and airgapped"}
{"id": "SIH1388", "title": "Detection of Malwarei Trojan in softwares used in Power Sector.", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Ministry of power", "text": "Title: Detection of Malwarei Trojan in softwares used in Power Sector.. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Ministry of power. Description: The power system networks are getting automated and software\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s are being updated or new Software\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s are being used in the network for various purposes including SCADA, Head End System, Meter Data Management System, Billing System, etc. Most of these software\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s are loaded in demilitarized zones regular patch updates and penetration tests are normally avoided on the live systems. These systems become vulnerable, and hackers try to exploit such systems using various attack vectors. The challenge is to validate presence of malicious codes if any in the software\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s which could exploit specific attacks including the zero day attack."}
{"id": "SIH1387", "title": "Detection of embedded Malware Trojan in hardware devices used in Power Sector.", "category": "Blockchain  Cybersecurity", "subcategory": "Hardware", "organization": "Ministry of power", "text": "Title: Detection of embedded Malware Trojan in hardware devices used in Power Sector.. Category: Blockchain  Cybersecurity. Subcategory: Hardware. Organization: Ministry of power. Description: We know that the technology is changing fast and so are the devices used in Power Systems network. The hardware devices used in the sector are also having fast processing capacity and are intelligent. They also communicate data either periodically or on request or if some logic is met or at programmed intervals, to control centers or to local  zonal SCADA system. The devices could be Intelligent Electronic Devices IEDs like Relay, BCU, Smart Meters, or Remote Terminal Units RTU etc. As these are electronic devices, they are prone to security threats. To make sure these devices are free from security threats, it is required to test them for malware  Trojan or alike of malicious codes present in the devices hardware systems like System on Chip Microcontrollers  Microprocessors DSP FPGA based products which has inbuilt firmware and dedicated application programs running within available and constraint memory. The challenge is to validate such electronic equipment\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s for vulnerability assessment tests and for presence of suspicious or malicious codes present if any, in the devices such codes could otherwise exploit specific attacks which may cause damage to process system or harm the environment and living beings on certain conditions or may trigger on logics including the zero day attack."}
{"id": "SIH1386", "title": "Developing a software for dubbing of videos from English to other Indian regional languages.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Developing a software for dubbing of videos from English to other Indian regional languages.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: CIPAM is engaged in the creation of promotional and awareness videos on Intellectual Property in India. A software that can be used for dubbing of videos from English to other Indian regional languages would help in mass outreach of such videos for public awareness. The software must produce a voiceover in a human like voice in Indian vernacular languages like Hindi, Marathi, Bengali, Gujarati, Tamil, Telugu, etc. as well as text supers that is dubbed from English to other Indian regional languages. The translated voiceover must also be in simple language, easy to understand and must not be colloquial in nature. A sample video has been provided for reference."}
{"id": "SIH1385", "title": "Developing a software that can translate resource material and other texts from English to other Indian regional languages.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Developing a software that can translate resource material and other texts from English to other Indian regional languages.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: CIPAM is actively involved in furthering IPR awareness, commercialization and enforcement. In this regard, CIPAM has created various educational tools and resource materials for easy understanding by students, industries, general public, police, judiciary and custom official. For wider outreach there is a requirement for a software that can translate these resource materials and texts from English to other Indian regional languages. The software should assist in translation of these texts created in multiple forms word document, pdf document, text in images etc. The software should aid in generating output that captures the true meaning of the text and not just the literal meaning. It should provide the option of generating translated texts in the following languages Hindi, Marathi, Bengali, Gujarati, Tamil and Telugu. The translated texts must also be in simple language, easy to understand especially by the general public and must not be colloquial in nature. A sample text has been provided for reference."}
{"id": "SIH1384", "title": "Developing an interactive gaming software  mobile application on Inteliectual Property Awareness for school students", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Developing an interactive gaming software  mobile application on Inteliectual Property Awareness for school students. Category: Smart Education. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: One of the primary objectives of CIPAM is to create educational tools and resources for Intellectual Property Awareness at school level. School students learn best when technical areas of law are gamified and interactive in nature. The interactive gaming softwaremobile application should aim at teaching the basics of Iintellectual Pproperty Rrights IPR to students, including Patents, Trademarks, Copyrights and Designs that can be used both inside and outside of the classroom and out. The game can have different activities on the IPs mentioned above, arranged at different levels of progression basic, intermediate and advanced. It should also include elements such as scoreboard, map of progress, factual titbits and a quick recap at the end. On completion of the game, the students should be able have a basic understanding of these IPs as a learning outcome."}
{"id": "SIH1383", "title": "Optimizing Doctor Availability and Appointment Allocation in Hospitals through Digital Technology and Al Integration.", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Govt of Himachal Pradesh", "text": "Title: Optimizing Doctor Availability and Appointment Allocation in Hospitals through Digital Technology and Al Integration.. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Govt of Himachal Pradesh. Description: To develop a digital system that streamlines the appointment scheduling process in hospitals by automating the process of identifying doctor availability and appointment slot allocation. The system will utilize advanced technologies such as RFID, face detection, proximity of Mobile phone, or any other relevant technology to detect the presence of doctors in the hospital. The system will use Artificial Intelligence AI to allocate appointment slots based on the doctors presence and the number of waitlisted patients. This will improve the overall patient experience by reducing the wait time. In conclusion, the proposed digital system will improve the efficiency and convenience of the appointment scheduling process in hospitals the patients will benefit with reduced waiting time."}
{"id": "SIH1382", "title": "RealTime Vehicle Tracking system.", "category": "Smart Vehicles", "subcategory": "Software", "organization": "Govt of Himachal Pradesh", "text": "Title: RealTime Vehicle Tracking system.. Category: Smart Vehicles. Subcategory: Software. Organization: Govt of Himachal Pradesh. Description: To develop a smart transportation system, which that aims to provide realtime information to the users regarding the availability of buses and their upcoming timings. The system will utilize advanced technologies such as GPS and other relevant services to gather the necessary data for providing reliable information to the users. The proposed system will have to be designed to facilitate the users in identifying the current location of their desired bus, along with the estimated time of its arrival at the respective bus stop. This will be achieved through the integration of the realtime data from the buses, as well as from the traffic management system. In conclusion, the proposed smart transportation system will significantly enhance the efficiency and convenience of the public transportation system in Himachal Pradesh. As a sustainability information to the user, good to indicate emission compliance of the bus e.g. Bharat Stage IV also if the bus uses clean fuel such as CNG or Electricity"}
{"id": "SIH1381", "title": "Analysis and identification of malicious mobile applications", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Ministry of Power", "text": "Title: Analysis and identification of malicious mobile applications. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Ministry of Power. Description: ln todays world, using different mobile applications for specific tasks is very common. This leads to smart phone users accumulating too many applications over a period. Seldom do users delete unused applications. Any application performing malicious tasks can very easily go unnoticed. So, there is a need to develop a mobile app tool that can use opensource intelligence and threat feeds to detect various indicators of compromise in the smartphones. The tool can check network communication to various lP addresses that are suspicious, various URLs that are suspicious, inbound connections or packets from applications that are suspicious."}
{"id": "SIH1380", "title": "lntelligent chatbot to answer queries pertaining to various Maintenance Processes within Substation", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Power", "text": "Title: lntelligent chatbot to answer queries pertaining to various Maintenance Processes within Substation. Category: Smart Education. Subcategory: Software. Organization: Ministry of Power. Description: Substation Asset Maintenance includes various maintenance activities for various equipment classes such as Transformer, Reactors, Circuit Breaker, lnstrument Transformers, Surge Arrestors etc. Maintenance activity for all these equipments include carrying out various tests and checks for which procedures along with acceptable limits are documented. Need is for creating an intelligent chatbot based on natural language processing which may aid in answering user queries pertaining to various maintenance activities. Examples of such queries include steps to carry out a test, its probable values acceptable limits, actions to resolve any issue faced during maintenance. The chatbot should have features for semantic processing of queries. It should also includes industrial standards and safety guidelines and test equipment to follow that activity."}
{"id": "SIH1379", "title": "Vegetation measurement along the line corridor using satellite imagery", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Software", "organization": "Ministry of Power", "text": "Title: Vegetation measurement along the line corridor using satellite imagery. Category: Agriculture, FoodTech  Rural Development. Subcategory: Software. Organization: Ministry of Power. Description: Solution is required for identifliing vegetation height below the Transmission conductor and raising alarm for lopping them. For this satellite imagerybased analysis is required along with trained machine learning model to simulate growth pattern. The growth pattern of different trees is different. The solution should be able to show the current height after adding the predicted increase in height from the date of capturing to till date to the height derived from satellite imagery."}
{"id": "SIH1378", "title": "Segregating and information on regulatory  Gofi. PolicieVGuidelinesOffice Memorauda etc. related to Contract and Procurement issues for proper compliance and reporting of the same.", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Power", "text": "Title: Segregating and information on regulatory  Gofi. PolicieVGuidelinesOffice Memorauda etc. related to Contract and Procurement issues for proper compliance and reporting of the same.. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Power. Description: For adhering to compliance of various procurement and contract management related Regulations Policies Guidelines Circulars Manuals Office Memorandums etc. issued by Govt. of India MoF MoP CVC from time to time for system improvement in the procurement process, such regulations  policies Guidelines Circulars etc. may require to be put in one place for information, reference, compliance  reporting of the same by the procuring authorities in the required manner like whether adopted in the tender document reporting thereof to the concerned made, wherever necessary through creation of a tool application system for effective contract management. The GUI should be user friendly and the documents should be easily accesible."}
{"id": "SIH1377", "title": "Creation of Live Digital Twins for the power Projects and integration with all existing monitoring and database system which will give a holistic real time approach to the project and plant from all aspect of construction, operation and maintenance.", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Power", "text": "Title: Creation of Live Digital Twins for the power Projects and integration with all existing monitoring and database system which will give a holistic real time approach to the project and plant from all aspect of construction, operation and maintenance.. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Power. Description: The comprehensive model should be able to feedin real time data input and give the necesaary output parameters. For example for a Hydro Project , the inflow input of water in the river should be able to give the real time data ouput such as Energy output, Spillingif any, Head loss, etc. .It would be a similar dynamic digital replicamimicry of the real project."}
{"id": "SIH1376", "title": "Robotics for inspection of abrasion  corrosion of underwater equipment  parts and further repair and maintainance", "category": "Robotics and Drones", "subcategory": "Hardware", "organization": "Ministry of Power", "text": "Title: Robotics for inspection of abrasion  corrosion of underwater equipment  parts and further repair and maintainance. Category: Robotics and Drones. Subcategory: Hardware. Organization: Ministry of Power. Description: Remotely Operated Vehicle ROVAutonomous Underwater Vehicle AUV should be able to identify and repair the localised problems of the underwater portion of the structures like , Penstock , Dam, Turbine , Surgeshaft etc in a Hydro Power Project Station without intervention of Human Deep Divers. It should preferabbly be indigneous and low cost in nature."}
{"id": "SIH1375", "title": "Geo tagging of plantation in the catchment area of hydro project", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Hardware", "organization": "Ministry of Power", "text": "Title: Geo tagging of plantation in the catchment area of hydro project. Category: Agriculture, FoodTech  Rural Development. Subcategory: Hardware. Organization: Ministry of Power. Description: The idea is to create a system that would keep a track on growth rate and existence of the plantation in the CAT Catchment Area Treatment  area. It would also check illegal forest activities."}
{"id": "SIH1374", "title": "Cloudburst prediction system", "category": "Disaster Management", "subcategory": "Software", "organization": "Ministry of Power", "text": "Title: Cloudburst prediction system. Category: Disaster Management. Subcategory: Software. Organization: Ministry of Power. Description: analyzing meteorological parameters and weather patterns can provide valuable information for predicting the possibility of cloudbursts. Local meteorological agencies and weather forecasting organizations."}
{"id": "SIH1373", "title": "Onestop solution for monitoring dairy plant energy consumption, hygiene and packaging waste collection from consumers.", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Hardware", "organization": "Ministry of Fisheries, Animal Husbandry and Dairying", "text": "Title: Onestop solution for monitoring dairy plant energy consumption, hygiene and packaging waste collection from consumers.. Category: Agriculture, FoodTech  Rural Development. Subcategory: Hardware. Organization: Ministry of Fisheries, Animal Husbandry and Dairying. Description: To provide a sustainable solution with regards to dairy plant operation and performance, the monitoring of plants energy demand per unit of produce would be an essential parameter. Monitoring of plant hygiene and sanitation would play a key role through the perspective of improvements in food safety. On the consumer end of dairy product value chain, the efficient and easy mechanism for collection of packaging waste is very important aspect. It is the need of the time to have onestop solution which would address the above mentioned aspects reliably with an integrated approach. Solution may be developed preferably based upon software integrated with associated hardware."}
{"id": "SIH1372", "title": "development of systems for effective Environmental, Social and Governance ESG Intervention in Higher Education", "category": "Renewable  Sustainable Energy", "subcategory": "Software", "organization": "Government of Jharkhand", "text": "Title: development of systems for effective Environmental, Social and Governance ESG Intervention in Higher Education. Category: Renewable  Sustainable Energy. Subcategory: Software. Organization: Government of Jharkhand. Description: It is well established that development towards a more sustainable society must begin with education. When education is done right, it not only teaches people about the science of climate change and inequality and the reasons we must take action, it also engages, empowers and promotes a more environmentally friendly, communitybased way of life. ESG in higher education is one of least discussed aspects of governance among administrators. With the recent energy and climate crisis, administrators around the globe have realized the importance of sustainable process. The three pillars of ESG are people, processes, and product. In our case, people are the most important stakeholder in achieving our goal. Students from campuses of Higher Education are not only woke about the climateenergy crises but also are passionate for deigning a world of their future. Administrators on other hand are aware of the rising cost of energy and are open to adopting energy conservation and other aspects of ESG The objective of the problem statement solution shall be to create a common platform for planning, development, execution, and monitoring of ESG initiative. The initiatives may include adopting alternative sources of energy, development of energy efficient campuses, adopting green construction standards like GRIHA Prakriti and similar such initiatives. Another objective of the problem statement solution is to generate and produce dynamic dashboards which show targeted lacking areas as well as progress check for currently undertaken projects. The overall objective of the solution should be based on a collaborative effort between all the stakeholder for creating ESG framework in Higher Education. If executed effectively the solution will not only make a significance difference to the present scenario but also create a generation of environmentally and socially aware citizens with in depth knowledge on ESG issues. The solution should be in line with Indias ambition of achieving its targets under COP 21 of Paris Agreement. It should also incorporate the best practice steps taken by governments like the International Solar Alliance. The overall objective of the solution shall be to develop a common monitoring dashboard for all ESG Initiatives taken by the state and predictive solution based on best practices across the globe."}
{"id": "SIH1371", "title": "Community Based Reporting and Monitoring Tool for Womens Safety in CollegesUniversities.", "category": "Miscellaneous", "subcategory": "Software", "organization": "Government of Jharkhand", "text": "Title: Community Based Reporting and Monitoring Tool for Womens Safety in CollegesUniversities.. Category: Miscellaneous. Subcategory: Software. Organization: Government of Jharkhand. Description: Campuses of colleges and universities are meant to be vibrant, freeflowing, and dynamic in nature. An accommodating campus supports idea exchange, personal growth, and soft skill development. The safety of students, especially womens students, is a pillar of an accommodating campus. In the present scenario, the measures available to college administrators and students, such as the installation of CCTV cameras, increased security on campuses, and the establishment of police outposts, are reactive in nature, i.e., they are pressed into action only after the occurrence of a mishap. The objective of the problem statement is to develop predictive analytic models to prevent mishaps even before they occur. The second issue pertaining to womens safety is the lack of manpower for proactive interventions to prevent mishaps. Another objective is to develop, monitor, predict, and provide actionable intelligence for the prevention of mishaps. The solution can explore the contours of anonymous and nonanonymous data collection mechanisms, pointtopoint reporting systems, and predictive data analytics for providing actionable intelligence. Further, the collected data can be leveraged to develop and mark probable black spots and red time zones for pinpointed actions to be taken by administrators. To promote a communitybased system, the system may be designed in such a way that it not only gathers information from students but also involves the entire student community in the prevention of mishaps. The data collected from the participants can be populated into the system, and realtime monitoring can be done with an interactive dashboard and charts. It can also be used to develop realtime rapid intervention by the student community, college administration, and local authorities. Further to this, a companion model can also be developed for students that helps them travel through black spots and during red time zones. This will not only ensure the community participation of students but also develop a responsibilitysharing framework for campus safety."}
{"id": "SIH1370", "title": "Real time monitoring of infrastructure development", "category": "Smart Automation", "subcategory": "Software", "organization": "Government of Jharkhand", "text": "Title: Real time monitoring of infrastructure development. Category: Smart Automation. Subcategory: Software. Organization: Government of Jharkhand. Description: Today, each sector, such as education, health, agriculture, etc., is growing at a fast pace, and so is the infrastructure required for each sector. Developing infrastructure for each sector is a huge task and involves huge costs. Currently, a few tools are being used to monitor the daytoday progress of the infrastructure, but they are all manual. This results in additional costs and the untimely completion of the infrastructural projects thus, it affects the overall development of any sector. For further clarification, consider the infrastructure development associated with the education sector. If any government wants to set up a technical institute, it has to go through multiple processes, and one of the major processes is the timely establishment of the institute. With geotagging, it is possible to identify the chunk of land within the given location, district, town, etc., but the major challenge lies when it comes to the development of infrastructure. Currently, there is no mechanism available to monitor the progress on a realtime basis, so lots of manual intervention happens to monitor the progress. To overcome this issue, it is necessary to develop GIS and Albased solutions through which realtime monitoring of the infrastructure can be done. Further, an application can also be developed that can work in both online and offline mode and through which daytoday progress can be fed into the system. Thus, collected data can be populated into the system, and realtime monitoring using interactive dashboards and charts is possible. Further, the system will also provide flexibility to the user through simulation interference, which will help the user analyze the impact of an external factor on the time required for completion of the project."}
{"id": "SIH1369", "title": "Online integrated platform for projects taken up by the students of various universitiescolleges", "category": "Smart Education", "subcategory": "Software", "organization": "Government of Jharkhand", "text": "Title: Online integrated platform for projects taken up by the students of various universitiescolleges. Category: Smart Education. Subcategory: Software. Organization: Government of Jharkhand. Description: Innovation is the key to betterment of education and students in the Indian universitiescolleges put a lot of efforts on the projects as a part of the academic requirements. If a common knowledge platform with a facility for plagiarism is created to bring all project works taken up at various levels by the students in Technical  Higher Educational Institutes and Universities throughout the country, then it will be a great source of knowledge and also will help the student community to take up uniqueinnovative project works Summary An integrated platform should be developed where in all the universitiesColleges provide information about the projects done by the students. The information on this platform will help in the peer learning and this will also help in cross functional research between various universitiescolleges. Objective To develop an online integrated platform for projects taken up by the students of various universitiescolleges."}
{"id": "SIH1368", "title": "Despite prohibition of hazardous cleaning of sewers and septic tanks manual cleaningof sewers and septic tanks without safety kits, safety devices and without adherence tosafety precautions it is still being resorted to in many parts of the country.", "category": "Smart Automation", "subcategory": "Hardware", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Despite prohibition of hazardous cleaning of sewers and septic tanks manual cleaningof sewers and septic tanks without safety kits, safety devices and without adherence tosafety precautions it is still being resorted to in many parts of the country.. Category: Smart Automation. Subcategory: Hardware. Organization: Ministry of Social Justice and Empowerment. Description: Despite prohibition of hazardous cleaning of sewers and septic tanks manual cleaning of sewers and septic tanks without safety kits, safety devices and without adherence tosafety precautions it is still being resorted to in many parts of the country. As a result,the reports of death of workers while cleaning sewers and septic tanks are still being highlighted by media. Presence of gases like hydrogen supplied, ammonia, methane, carbon monoxide and Sulphur dioxide in sewers and septic tanks, beyond certain limits, make the atmosphere in the sewers and septic tanks, hazardous, resulting in fatal accidents. Cleaning of sewers and septic tanks can still be risky even with the use of PPE Kit, Safety devices. Solution At present, there are gas monitors of various kinds, which are available. There is a need of a device which can monitor the availability of these gases while a worker is on the job of cleaning, so that the personssupervisor available outside the sewerseptic tank can get alarmnotification that the atmosphere in the sewerseptic tank is not suitable for entryworking in a sewer or septic tank. This device can save the lives of many people working in sewers and septic cleaning operations."}
{"id": "SIH1367", "title": "To develop a technical solution for enabling Institution level verification of students of one State studying in other States, who are at present generally denied benefits under the Scholarship scheme as the Institutions in which they are studying are not registered on the portals of their home State.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: To develop a technical solution for enabling Institution level verification of students of one State studying in other States, who are at present generally denied benefits under the Scholarship scheme as the Institutions in which they are studying are not registered on the portals of their home State.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Social Justice and Empowerment. Description: To develop a technical solution for enabling Institution level verification of students of one State studying in other States, who are at present generally denied benefits under the Scholarship scheme as the Institutions in which they are studying are not registered on the portals of their home State."}
{"id": "SIH1366", "title": "Centralised Nasha Mukti Database", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Centralised Nasha Mukti Database. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Social Justice and Empowerment. Description: Details of counselling and DeAddiction interventions provided to the beneficiaries at facilities supported by MoSJE is not available on a single platform, which could help in analysis of patientwise, center wise or State wise details of services provided, beneficiaries reachedand other relevant details."}
{"id": "SIH1365", "title": "Online Blockchain based certificate generation and validation system for government organization.", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Government of Gujarat", "text": "Title: Online Blockchain based certificate generation and validation system for government organization.. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Government of Gujarat. Description: Currently large no of training programs is organized, and certificates are provided. There is no mechanism to validate digital certificate.so create a system in which custom digital certificate generate. User can store certificate in digital locker system other organization will validate certificate. Use opensource software and blockchain technology. Expected Output Blockchain Based Certificate generation and validation Certificate can be added in Digital Loker System Users Government Office, Student, Industry, Institutes"}
{"id": "SIH1364", "title": "Fake Social Media Profile detection and reporting", "category": "Blockchain  Cybersecurity", "subcategory": "Software", "organization": "Government of Gujarat", "text": "Title: Fake Social Media Profile detection and reporting. Category: Blockchain  Cybersecurity. Subcategory: Software. Organization: Government of Gujarat. Description: The social life of everyone has become associated with the online social net works. These sites have made a drastic change in the way we pursue our social life. Making friends and keeping in contact with them and their updates has become easier. But with their rapid growth, many problems like fake profiles, online impersonation have also grown. Fake profiles often spam legitimate users, posting inappropriate or illegal content. Several signs can help you spot a social media fake who might be trying to scam your business. Identifying fake social media profiles and taking corrective measures. Expected Output An Application software that detects the fake social media profile Users Crime branch and other investigative agencies"}
{"id": "SIH1363", "title": "Selfidentifying the mental health status and get guidance for support.", "category": "Fitness  Sports", "subcategory": "Software", "organization": "Government of Gujarat", "text": "Title: Selfidentifying the mental health status and get guidance for support.. Category: Fitness  Sports. Subcategory: Software. Organization: Government of Gujarat. Description: Considering the increasing burden of the mental disorders as evidenced in National Mental Health Survey2016, it is important to identify the people at the risk of developing mental disorder at early stage to take the necessary action. Primary Health Care centre is a gatekeeper of the Indian public health care delivery system and also an opportunity to screen patient for the risk of developing mental disorders. There are some validated tools are available for screen of the person for risk of developing psychiatric disorders, however, ready availability, taking response from patients, interpretation and quick guide for taking action based on the interpretation of the tool score is still challenge for effective and efficient utilization of the screening tool. Expected Output Mobile application for screening of mental health. Users Public as well as Frontline Health worker."}
{"id": "SIH1362", "title": "Student dropout analysis for school education", "category": "Smart Education", "subcategory": "Software", "organization": "Government of Gujarat", "text": "Title: Student dropout analysis for school education. Category: Smart Education. Subcategory: Software. Organization: Government of Gujarat. Description: Right to education is key concern for government and at school level drop out ratio is high due to poverty and social, economic reasons. If government have drop out student analysis on following different categories, it will be very useful in framing different policies. 1. School wise 2. Area wise 3. Gender wise 4. Caste wise 5. Agestandard wise Expected Output Focused interventions on the high dropout rates"}
{"id": "SIH1361", "title": "Development of Small Scale Wind energy device", "category": "Renewable  Sustainable Energy", "subcategory": "Hardware", "organization": "Government of Gujarat", "text": "Title: Development of Small Scale Wind energy device. Category: Renewable  Sustainable Energy. Subcategory: Hardware. Organization: Government of Gujarat. Description: Urban landscape and the physical challenges restrict the erection of smallscale wind turbines. Largescale turbines fared well compared to smallscale wind turbine models. Other general challenges are as under 1 Highly controlled energy sector 2 lack of awareness and information 3 restricted access to technology 4 lack of competition 5 high transaction costs 6 poor market infrastructure 7 High investment requirements Expected Output Ecofriendly energy alternative, Unlike energy produced by fossil fuels while wind energy is completely clean and ecofriendly. Not only is wind power free to harness, but its also 100 renewable."}
{"id": "SIH1360", "title": "Price comparison of GeM products with other emarketplaces", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Price comparison of GeM products with other emarketplaces. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: The problem at hand involves developing a cost or price comparison solution specifically tailored for comparing the prices of products available on GeM Government eMarketplace with other emarketplaces or ecommerce platforms. GeM is a dedicated online platform in India that facilitates the procurement of goods and services by various government departments, organizations, and public sector undertakings. Comparing the prices of products listed on GeM with those on other platforms is crucial for ensuring fair and competitive pricing. The cost or price comparison solution aims to provide users with a convenient way to compare the prices of products available on GeM with other popular emarketplaces or ecommerce platforms. By leveraging data scraping techniques, APIs, and data analytics, the solution will gather and analyze pricing information from multiple sources, allowing users to make informed decisions based on the best available options."}
{"id": "SIH1359", "title": "Contact center knowledge management tool with decision tree", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Contact center knowledge management tool with decision tree. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: The problem at hand involves developing a knowledge management tool specifically designed for contact centers, aiming to streamline customer support and enhance the efficiency of agents through the implementation of a decision tree framework. Contact centers handle a vast amount of customer inquiries, and providing accurate and consistent responses is crucial for delivering highquality customer service. The knowledge management tool with a decision tree is designed to assist contact center agents in accessing relevant information quickly and making informed decisions when interacting with customers. It leverages a decision tree structure, which is a hierarchical model that guides agents through a series of questions and steps to determine the appropriate response for a given customer query or issue."}
{"id": "SIH1358", "title": "Image correctness for a product on marketplace", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Image correctness for a product on marketplace. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: This problem involves developing an image correctness analysis solution specifically tailored for assessing the accuracy and quality of product images on marketplace and ecommerce platforms. With the increasing reliance on visual content in online shopping, ensuring the correctness and authenticity of product images has become crucial for building trust and facilitating informed purchasing decisions. Image correctness analysis refers to the process of automatically evaluating and verifying the accuracy, completeness, and relevance of product images displayed on online marketplaces and ecommerce platforms. It aims to detect potential issues such as misleading images, incorrect representations, altered visuals, or mismatched product details."}
{"id": "SIH1357", "title": "Sentiment analysis of Social Media presence", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Sentiment analysis of Social Media presence. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: This problem involves developing a sentiment analysis solution specifically designed for analyzing the sentiment expressed in the social media presence of individuals and organizations. With the significant impact of social media on personal and organizational reputation, understanding the sentiment of social media posts, comments, and interactions has become essential for individuals and businesses alike. Sentiment analysis refers to the process of automatically determining the sentiment or emotional tone conveyed by text or speech. In the context of social media, sentiment analysis can provide valuable insights into public perception, customer feedback, and brand reputation. By analyzing the sentiments expressed in social media content, individuals and organizations can gauge the overall sentiment trends, identify potential issues, and take appropriate actions to maintain or enhance their online presence."}
{"id": "SIH1356", "title": "Sentiment Analysis of Incoming calls on helpdesk", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Commerce and Industries", "text": "Title: Sentiment Analysis of Incoming calls on helpdesk. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Commerce and Industries. Description: The problem at hand involves developing a sentiment analysis solution specifically tailored for analyzing the sentiment of incoming calls in helpdesks, call centers, and customer services. With the everincreasing volume of customer interactions in these domains, it is crucial for businesses to gain insights into the sentiments expressed by their customers during phone conversations. Sentiment analysis refers to the process of automatically determining the sentiment or emotional tone conveyed by a text or speech. In the context of incoming calls, sentiment analysis can provide valuable information about customer satisfaction, identify potential issues, and highlight areas for improvement in customer service delivery."}
{"id": "SIH1349", "title": "Using existing CCTV network for crowd management, crime prevention, and work monitoring using AIiML", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Railways", "text": "Title: Using existing CCTV network for crowd management, crime prevention, and work monitoring using AIiML. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Railways. Description: The Indian Railways is one of the largest railway networks in the world, serving millions of passengers daily. However, with the increasing number of passengers and trains, the management of railway stations and trains has become a challenge, especially when it comes to crowd management, cleanliness, crime prevention, and work monitoring. The traditional methods of manual monitoring and surveillance are time consuming, and human error can lead to missed incidents. The integration of AI and ML technology can help the Indian Railways to overcome these challenges. Albased CCTV networks can analyze large amounts of data in realtime and provide insights into crowd management, crime prevention, and work monitoring. This can improve the safety and security of passengers, as well as the efficiency of railway operations. For example, AI algorithms can detect unusual behavior and alert security personnel, while ML algorithms can predict crowd patterns and help with resource allocation. However, implementing Albased GCTV networks requires a significant investment in technology and infrastructure, as well as the development of data management systems that can handle the large amount of data generated by these systems. Additionally, privacy and ethical considerations must be taken into account to ensure that the use ofAI technology does not infringe on the rights ofpassengers or workers. In conclusion, the use of AI and ML technology in the analysis of existing ccrv networks of the Indian Railways can bring about significant benefits for crowd management, crime prevention, and work monitoring. However, careful planning and implementation are required to ensure that these benefits are realized while respecting the privacy and ethical concerns ofstakeholders"}
{"id": "SIH1348", "title": "Natural language translation engine for announcements and information dissemination at stations", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Railways", "text": "Title: Natural language translation engine for announcements and information dissemination at stations. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Railways. Description: Design ofa system to provide information in a desired Indian language on demand by passengers and other customers, in written and oral form. The system should be extendable to foreign languages for tourists as and when required. Limited vocabulary systems for commonly required railway information services are acceptable. Scope ofthe system  announcements at stations, information over IVRS, information through chatbots and web interfaces. constraints to be considered  voice recognition in different languages noisy ambience at stations adequate computing power for onthefly content generation delivery on mobile devices."}
{"id": "SIH1347", "title": "A software that suggests drugs and formulations for a diseasepharmacological property based on the Ayurvedic classical booksRepositories.", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: A software that suggests drugs and formulations for a diseasepharmacological property based on the Ayurvedic classical booksRepositories.. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of AYUSH. Description: Ayurveda has a large database of single herbs, minerals, and formulations that have been tailormade to suit each individual, hisher psychosomatic constitution, clinical condition, comorbidities, age, region, etc. These data are spread over more than 150 texts, amidst manuscripts in multiple languages and scripts. With the rise of transcriptional and translational facilities, several traditional medicinal texts are now available in their digitized forms. But for an Ayurvedic student or practitioner, exploring this multitude of literature for identifying their drug of choice often becomes tedious and impractical. Here is the need of a custom software that can identify the apt formulation that has been designed to treat a constellation of symptoms and present it to the studentpractitioner along with its reference and other desired properties. For example, the two formulations Punarnavadi Kashaya and Vyaghryadi Kashaya are mentioned in texts as follows Both these are decoctions advised in Jvara fever and Kasa cough. But in a patient with upper respiratory tract infections like common cold Pinasa, Vyaghryadi would suit better than Punarnavadi. Whereas in a condition associated with inflammatory changes all over the body. I Punarnavadi would be the appropriate choice. The objective of the proposed software is to identify the single drugs and formulations that suit a set of symptoms. Certain ingredients eg. jaggery are unsuitable for certain categories of patients e.g. diabetics. There are also medicine mediums that are unsuitable for specific diseases e.g. fermentedalcoholic preparations in diabetes. Such information is also expected to be conveyed to the learner or practitioner who uses the software. The same disease has been mentioned in different names E.g. Jvara and Santapa for fever and the same word has been used to denote different Eg. Abhaya generally means Terminalia chebula but in the context of Jatyadi ghrita, it means Vetiveria zizanioides. The multiple names of same diseases are expected to be included in the tags of each formulation. The sources for the formulations, and synonyms and similar words have been included in the data section. It is also desirable to include the Ayurvedic pharmacological properties of the single drugs, and the compound formulation called Rasa, Guna, Virya, Vipaka, etc. as and where available."}
{"id": "SIH1346", "title": "Chatbot to Known Individual Prakriti Phenotype", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: Chatbot to Known Individual Prakriti Phenotype. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of AYUSH. Description: Ayurveda is a natural health care system that emphasizes the treatment of disease in a highly individualized manner as it believes that every individual is unique having a different constitution. It classifies all individuals into different Prakruti types based on the theory of tridosha and each type has a varying degree of predisposition to different diseases. This is independent of racial, ethnic, or geographical considerations. Knowing which particular Prakruti one belongs to, enables them to decide the preference of food which suits them best. By following the rules and regulations regarding food and lifestyle, one can prevent themselves from getting inflicted with various diseases. But, Prakruti assessment is a tedious process that requires the individual to visit the physician followed by a long list of questions in order to reach a conclusion. In order to make this process more convenient, the development of a self assessment tool with the help of artificial intelligence may prove to be a stepping stone. A Chatbot which has prerecorded questionnaire may help in the Prakruti assessment depending upon the responses given by an individual."}
{"id": "SIH1345", "title": "StartupAYUSH Portal", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: StartupAYUSH Portal. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of AYUSH. Description: The key objective of the Startup AYUSH portal is to be a onestop platform for all stakeholders Startups, Investors, Incubators, Accelerators, Government Agencies, and Public Users in the AYUSH system to interact and collaborate in a highly dynamic environment. The portal is directed to provide a collaborative platform for all the stakeholders of the AYUSH startups to have an interactive engagement to enhance and bolster the network. It will bring together the entire AYUSH Startup community at the global level through virtual connections, mentorship, and showcase opportunities. It will have a plethora of resources and information guides to propel everyone in their entrepreneurial journey."}
{"id": "SIH1344", "title": "Albased tool for preliminary diagnosis of Dermatological manifestations", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: Albased tool for preliminary diagnosis of Dermatological manifestations. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of AYUSH. Description: The Global Burden of Disease project has shown that skin diseases continue to be the 4th leading cause of nonfatal disease burden worldwide. These conditions are often the presenting face of more severe systemic illnesses, including HIV and neglected tropical diseases NTD. such as elephantiasis and other lymphedemacausing diseases. Additionally, skin disorders pose a significant threat to patients wellbeing, mental health, ability to function, and social participation. However, it is very difficult to provide better dermatological care to underserved or resourcepoor regions in a costeffective manner owing to unavailability of efficient diagnostic tools, lack of connectivity, and poor laboratory infrastructure etc. Moreover, there is also a scarcity of physicians with dermatological training. Even, preliminary screening of a dermatological manifestation seems to be an arduous task. Thus, developing an Artificial intelligencebased tool through Image processing technique for preliminary diagnosis of numerous dermatological conditions will prove to be a boon in the health care system."}
{"id": "SIH1343", "title": "Identification of Different Medicinal PlantsRaw materials through Image Processing Using Machine Learning Algorithms", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: Identification of Different Medicinal PlantsRaw materials through Image Processing Using Machine Learning Algorithms. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of AYUSH. Description: ndia, with a rich heritage of floral diversity, is wellknown for its medicinal plant wealth, but their identification is one of the major burning issues in Ayurvedic Pharmaceutics. Several crude drugs are being sold under the same name in the market leading to confusion and their misidentification. Even the collectors and traders are not completely aware of the exact morphological appearance or differentiating attributes of the many drugs owing to seasonal and geographical availability, and similar characteristics. Moreover, the extensive consumption to meet demandsupply ratio exerts a heavy strain on the existing resources. It further leads to the practice of adulteration, substitution, and disbelief in the curative capability of the system eventually. Thus, software capable of identifying different medicinal plants raw materials through Image Processing Using Different Machine Learning Algorithms will be of immense use. It will be helpful at every level viz. wholesaler, distributor, etc. of the supply chain of raw material being utilized in the system."}
{"id": "SIH1342", "title": "Development of a prototype instrument sensor based for assessment and quantification of rasas taste in crude herbs.", "category": "MedTech  BioTech  HealthTech", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: Development of a prototype instrument sensor based for assessment and quantification of rasas taste in crude herbs.. Category: MedTech  BioTech  HealthTech. Subcategory: Software. Organization: Ministry of AYUSH. Description: Every herb used in the Ayurveda possesses some Taste Rasa and most important of them are Madhura Sweet. Amla Sour, Katu Pungent, Tikta Bitter, Kashaya Astringent. The rational application of herb in management of various diseases also depends on selection of herbs as per their rasa Taste as rasa Taste has the potential to effect the physiology of human body. Further, the same principle also applied for dietary items for their use in health management In fact, management of physiological units Vata, Pitta or Kapha Body humors may also be done on rational application of dravyastmaterials of dietary and medicinal value based on rasa taste. However, it is simple to identify rasa taste through tongue but it is not possible to quantify Further, the potency of herbs also depends on its rasa. Thus, quantification of rasa taste in crude herbs is also needed for quality assessment of crude herbs. Solution Need to develop an instrument Tongucometer which can quantify the Taste rasaMadhura Sweet, Amla Sour, Katu Pungent, Tikta Bitter, Kashaya Astringent present in the materials used as diets and medicine through a sensorbased instrument"}
{"id": "SIH1341", "title": "PG dissertation Management System Description.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: PG dissertation Management System Description.. Category: Smart Education. Subcategory: Software. Organization: Ministry of AYUSH. Description: PG dissertation is the partial requirement for the fulfillment of Pg degree. During 1st year of post graduation, student select PG dissertation topic and carry out research under supervision of PG guide and submit dissertation during final year of evaluation. The following are the major steps of the dissertation Selection of topics 1. As per the thrust ideas of research of a particular department. 2. Maintaining student guide ratio. 3. To avoid duplication. Approvals and ethical issues. Monitoring research progress. Evaluation of dissertation. Publication if any, out of dissertation research. Maintenance of database of all dissertations under various categories with search option. With holding of university final year results in case of disapproval of PG dissertation. Managing and monitoring all the above aspects throughout the country maybe a herculean task. An application to ease the process and improve the quality of research is essential."}
{"id": "SIH1340", "title": "Application for Assessment of Quality of Textbook Reference Books E Book", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of AYUSH", "text": "Title: Application for Assessment of Quality of Textbook Reference Books E Book. Category: Smart Education. Subcategory: Software. Organization: Ministry of AYUSH. Description: The medical students aiming to be good physicians, need to select a standard medical textbook. It is evident that there are several books made available in ASU Ayurveda, Unani, Siddha medical systems and every book claims that the book has been written as per CCIM or NCISM syllabus. Amongst some are good and some are of poor quality and were written with vested interest. Due to this, students are trapped by substandard books that leads to poor quality standard among students. Identifying this issue and its impact on medical on the medical system, NCISM constituted an expert committee for the development of an assessment scale for textbooks reference books. The expert committee after thorough deliberations developed the assessment scale for the quality assessment of textbooks reference books. This assessment scale serves the purpose of \u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00c2Selection of quality textbooks by teachers for their students. Serving as a reference for textbook writers. \u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00c2 Providing criteria to recommend for inclusion in the list of recommended apex regulatory bodies. The assessment process requires a review of the books by reviewers subjectwise reviewers located across the country, 3 reviewers per book and summarizing the reviewers remarks and decision by the committee. The development of an application on this will help to get reviews from people around the world online and also will help to summarize all the reviewed data in a faster mode."}
{"id": "SIH1339", "title": "Automation of drill core rock sample lithology logging.", "category": "Transportation  Logistics", "subcategory": "Software", "organization": "Ministry of Mines", "text": "Title: Automation of drill core rock sample lithology logging.. Category: Transportation  Logistics. Subcategory: Software. Organization: Ministry of Mines. Description: The lithology of drilled core samples in exploration is usually identified on the basis of grain size, color, cementing material, mixing of grains of different sizes sorting, packing and compactness of grains roundness of grains. The Luster and specific gravity of the sample also play a crucial role in the identification of core rock samples with respect to dimensions. The geological discontinuities are identified on the basis of fractures, joints, and lithocontacts. Hence, the development of a device assembly, that scans the drill core rock sample and identity the lithology and geological discontinuities on the basis of above said parameters by the artificial intelligence with given standards."}
{"id": "SIH1338", "title": "Unpredictable failure of poly pulleys along cable belt conveyor system for pulley changing", "category": "Transportation  Logistics", "subcategory": "Software", "organization": "Ministry of Mines", "text": "Title: Unpredictable failure of poly pulleys along cable belt conveyor system for pulley changing. Category: Transportation  Logistics. Subcategory: Software. Organization: Ministry of Mines. Description: There are more than 18,000 nos. of poly pulleys along the conveyor length which undergo wear and tear and need replacements including emergency replacements stopping the conveyor system. Solution Desired Based on past data and the use of suitable ML software, the pulley failures with its locations can be predicted which would help in avoiding sudden stoppages of the conveyor."}
{"id": "SIH1337", "title": "unpredictable wear and tear of cable belt conveyor Rope and belt leading to frequent stoppage of single line Mine production system causing significant loss of production", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Mines", "text": "Title: unpredictable wear and tear of cable belt conveyor Rope and belt leading to frequent stoppage of single line Mine production system causing significant loss of production. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Mines. Description: The 14.6 km long cable belt conveyor is supported on pulleys. A steel rope runs over these pulleys while the belt sits over them. The conveyor is operated by pulling the steel ropes with a drive motor. The rope gets elongated due to pulling force leading to breakage of its strands. Similarly, the belt also gets worn out causing stoppage of conveyor operation unpredictably. Solution Desired By capturing the past reasons for wear and tear including visuals and using a suitable ML application, try to predict the condition of the Rope and belt well in advance such that corrective and preventive actions can be taken before its failure preventing loss of production."}
{"id": "SIH1336", "title": "Mines operation specially haulage of dumphers done through operators extended even in the absense in adequacy of operators.", "category": "Miscellaneous", "subcategory": "Hardware", "organization": "Ministry of Mines", "text": "Title: Mines operation specially haulage of dumphers done through operators extended even in the absense in adequacy of operators.. Category: Miscellaneous. Subcategory: Hardware. Organization: Ministry of Mines. Description: Description In case of Force majeure situations like road blockadesstrikes by locals, all haulage operations are halted. Also sometimes due to the absence of operators, the quantum of excavationhaulage has to be reduced commensurate to the available workforce. Solution Desired If there would have been autonomousautomated haulage systems allowing programmable operation of dumpers within OEM operating parameters, without an operator sitting inside the cabin, then the issues could be taken care of. This would further \u00c3\u0192\u00c6\u00c3\u00c2\u00bc Reduceeliminate the need to stop equipment for breaks and shift changes, which increases the utilization of each vehicle. \u00c3\u0192\u00c6\u00c3\u00c2\u00bc By better tracking and controlling vehicle operations within OEM prescribed limits, asset life can be extended, including areas such as tires, brakes, and other components. \u00c3\u0192\u00c6\u00c3\u00c2\u00bc The systems having control from a remote controlcommand center will enable controlling and managing vehicle operations in a consistent manner thereby leading to a significant reduction in labor, fuel, and maintenance costs. In the absence of completely autonomousautomated haulage systems, teleoperated vehicles could be used enabling automatic steering along a preset path. \u00c3\u0192\u00c6\u00c3\u00c2\u00bc Drivable areas within the mine are converted into a map for the systems. \u00c3\u0192\u00c6\u00c3\u00c2\u00bc Here an operator may control acceleration and braking while the Teleoperating system automatically controls steering. Similar to a train on a railroad track, controlled vehicles can drive on their precise path, minimizing the reliance on highresolution video and operator skills for safe operation and also preventing the operators from driving in unsafe and prohibited areas."}
{"id": "SIH1335", "title": "Dificulty in operating Heavy earth moving machineries during rainy season 45 months due to extremely poor visibility conditions leading to significant loss of excavation and production", "category": "Transportation  Logistics", "subcategory": "Hardware", "organization": "Ministry of Mines", "text": "Title: Dificulty in operating Heavy earth moving machineries during rainy season 45 months due to extremely poor visibility conditions leading to significant loss of excavation and production. Category: Transportation  Logistics. Subcategory: Hardware. Organization: Ministry of Mines. Description: The Mine is located on a hill top over an altitude of avg. 1250m above MSL, suffers significant loss of excavation and production during 45 months of rainy seasons due to extremely poor visibility condition caused by stagnated dark clouds on the hilltop. It becomes very unsafe to operate Loaders and Dumpers resulting in huge loss of excavation and production. Solution Desired Loaders and Dumper operations should be guided in such a way that Mine excavation and production processes are carried out safely. The solution may lie in using suitable AIML applications based on GPS technology, realtime monitoring of weather conditions, and providing fogpiercing lights coupled with proximity sensors to avoid equipment collisions. The solution should be effective enough to operate mining machineries safely to meet the targets."}
{"id": "SIH1334", "title": "Software for intervention of speech sound disorders in Hindi and English.", "category": "Smart Education", "subcategory": "Software", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Software for intervention of speech sound disorders in Hindi and English.. Category: Smart Education. Subcategory: Software. Organization: Ministry of Social Justice and Empowerment. Description: Background The majority of individuals with intellectual disability, hearing impairment, cerebral palsy, chronic neurological condition, and speech and language disability present with speech sound disorders characterized by unintelligible speech. These errors may reflect a lack of motor skills to produce a sound, a lack of linguistic knowledge, or deficiencies in both. The Intervention approaches must focus on one or a combination of both skills. Hindi, the national language of India is spoken by over 1 million people, and 43 of the Indian population. Though several training resources are available in English, the resources in Indian languages are limited. A training resource manual titled \u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00c5Phonics and phonological processing to Develop Literacy and ArticulationPreliteracy and Articulation drill book\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2 contains the sounds of Hindi in isolation and in different words across all word positions. The same can be used to develop software for training individuals in perception and production intervention. Given that the majority of the Indian population is in the rural regions and human resources are in the urban regions, this software can help bridge the gap and serve the unserved population. The software can be used to give visual and auditory stimulation and feedback and can help track progress. The training can be more intense with short frequent sessions, and flexible timings. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Detailed description Motorbased Intervention generally includes perceptual and production training and both can be incorporated into the same software. Perceptual training also called ear training involves teaching their clients to perceive the distinction between the target and error sounds. Through software, the child can be presented with a variety of correct and incorrect realspeech examples of the target phoneme and can be asked to make judgments about whether the production was correct. The software can also provide visual feedback about the accuracy of the child\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s judgment. The software can also track the child\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s success rate to assist the clinician in monitoring progress. Production of a target sound can be done using imitation, phonetic placement, successive approximation shaping, and contextual utilization. All of these can be achieved using the software specifically developed for its use. For eg Imitation Several auditory models of the sound in isolation, syllables, or words can be provided. The child can be asked to watch and listen to the video that shows the 3 dimensional image of the sound word being produced and then can be asked to repeat the same. The client\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s production can also be recorded for selffeedback. Phonetic Placement When the client is unable to imitate a target sound, cues or instructions regarding where to place his or her articulators can be provided. Use of camera to serve as a mirror, images showing the placement, and videos showing the placement and movement during articulation can be developed. The videos would be 3 dimensional and could involve sound in isolation and word level. Contextual utilization Involves isolating a target sound from a particular phonetic context in which a client may happen to produce a sound correctly, even though he or she typically produces the sound in error. Eg The child can say \u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00c5bright sun\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2, but cannot produce\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00c5s\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2 in other context. Slowly introduce other context through the software and then progress to production of\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00c5s\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2 in isolation. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Description of the Solution For instance, the child needs to be taught the pronunciation of the syllable ka. 1. The word \u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00c5cat\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2 is projected along with the picture. 2. As we tap the word, k\u00c3\u0192\u00c6\u00c3\u00c2t is heard. This forms the auditory cue. 3. The placement of the tongue in the oral cavity and as its changes when the syllables ka \u00c3\u0192\u00c6\u00c3\u00c2 and t are pronounced is presented on the animation. This provides a visual cue. 4. Production of Isolated syllables eg. ka in this case, can also be shown. 5. The tongue movements inside the oral cavity can be shown in slow motion as well. A total of 213 such words for each phonemedepending on the frequency of occurrence of phoneme in the language , with each word having a maximum of 5 syllables can be shown to explain the articulatory pattern. The software can be developed in both Indian English and in Hindi, based on the data set."}
{"id": "SIH1333", "title": "Udyog Saarthi App  Progressive Webbased Application for Adults undergoing Job coaching for opportunities under 4 reservation in NIEPMD and other Institutions.", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Social Justice and Empowerment", "text": "Title: Udyog Saarthi App  Progressive Webbased Application for Adults undergoing Job coaching for opportunities under 4 reservation in NIEPMD and other Institutions.. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Social Justice and Empowerment. Description: Persons with disabilities in our country, despite the progress happening on accessibility, education, skills development, and employment, still face many challenges, especially in the employment phase of life that promotes inclusion in the family and society. Enhancing vocational training and employment prospects for people with disabilities is essential and there are efforts happening at the National level that has to end up in employment that fetches significant economic benefits and dignified life for them. As per the recent survey by the Ministry of Statistics report on Persons with disabilities, about 64 of persons with disabilities in India are not into employment. The vulnerable group of the population with disabilities under the D andamp E categories of 4 reservation in Government and PSU Jobs Persons with disabilities of type, Intellectual Disability, Autism Spectrum Disorder, Multiple Disabilities, Mental Illness, and Specific Learning Disabilities are either not aware of or not getting notifications regarding the employment vacancies, queries, or other such information due to the unavailability of access to print media, and online services like employment news. They do require a curated and accessible content with adaptations to specifically inform them of the job and details in a simple language with requirement information to apply, know the coaching facilities available and get trained to apply, appear for competitive examinations, get qualified and place themselves to achieve the 1 reservation allocated to them. In many scenarios, the parents of such adults are also not with much literacy, awareness about such job opportunities through their adult children are eligible for them. This results in many of the jobs though published not being applied, for or the right candidates getting a placement as per the reservation and implementation of the Rights of Persons with Disabilities, Act 2016. Detailed Description Accessibility and inclusion of Persons with disabilities PwDs in the workforce remains a challenging one on a global scale and with much more diversity in our Countrys context. There have been ongoing attempts and measures to ensure the inclusion of PwDs in the workforce especially in the Government and PSUs level by the Department and other agencies working for them. This includes the Job Coach programme, an introduction to job support services, with support for application, training for competitive examination in an accessible manner, guidance to the PSUs and Department for accessible conduct of competitive examinations, counselling, and logistic support for candidates to write  attend the examination and interview thereafter. Finally, on placement, support for workplace adaptations or reasonable accommodation to sustain the job. The Department of Empowerment of Persons with Disabilities DEPwD, National Institutions like NIEPMD, collaborating with Government and NonGovernmental Organizations NGOs support with such job coach programme and assist Persons with Disabilities PwDs especially D andamp E category, the most vulnerable group with either cognitive, learning, severe mobility, behaviour management, socialisation, or multiple such difficulties. Expected Solution The software application intends to \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Create a learning profile of the candidates The learning profile includes persons information with UDID, literacy, skills training, address, functional difficulties faced as per Washington Group Extended Set Questions, assistive devicessolutions used or required, human assistance requirements, aspiration profile. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Provide information in an accessible manner to the candidates  parents for various opportunities happening as per 4 reservation in Government and PSUs. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Additionally, inform jobs as per equal opportunity policies happening in private sector. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Notification on important dates related to jobs application, examination, and interview. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Support for model question paper, practice, and mock tests before qualifying examinations. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Tracking for applied, examination attended, interview qualified and placement. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Workplace support requirements requests for facilitation. \u00c3\u0192\u00e2\u0161\u00c3\u00c2 Department of Adult Independent Living DAIL, NIEPMD related other skills training programmes information notifications for getting enrolled, qualified with skills development."}
{"id": "SIH1332", "title": "Hot metal, Steel Ladle, and Scrap pot Tracking by autocapturing the Ladle number and locations at SMS1 and SMS2.", "category": "Transportation  Logistics", "subcategory": "Software", "organization": "Rashtriya Ispat Nigam Limited, Visakhapatnam Steel Plant", "text": "Title: Hot metal, Steel Ladle, and Scrap pot Tracking by autocapturing the Ladle number and locations at SMS1 and SMS2.. Category: Transportation  Logistics. Subcategory: Software. Organization: Rashtriya Ispat Nigam Limited, Visakhapatnam Steel Plant. Description: The ladle is identified by the number painted manually on its outer surface. The ladle number has to be captured. The exact location and number of the ladle has to be tracked in realtime and feedback is to be given to the local server for display. The hot metal Ladle travels from the TLC pit area to the converter for charging liquid hot metal and back. Steel Ladle travels from the Ladle preparation bay with liquid steel to secondary metallurgy units i.e. LF1, LF2, RH, or Twin LF, and then to one of the Caster machines for casting into billets and back. Slag Pot travels from Converter to the Slag dumping area and back. Benefits of solving this issue are tracking of the ladles, timely changes in process based on the ladle history, calculation of ladle circulation times, ladle life, and ladle turnaround times."}
{"id": "SIH1331", "title": "A complete Digital Marketing solution for visible increase in online sale of DPD\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s journals and Employment News issues through various availablenew online sale channels like website, apps , amazon, social media by optimising various available or new digital marketing tools like email marketing, SEO, analytics, etc., subject to suitability, sustainability and costeffectiveness.", "category": "Miscellaneous", "subcategory": "Software", "organization": "Ministry of Information and Broadcasting", "text": "Title: A complete Digital Marketing solution for visible increase in online sale of DPD\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s journals and Employment News issues through various availablenew online sale channels like website, apps , amazon, social media by optimising various available or new digital marketing tools like email marketing, SEO, analytics, etc., subject to suitability, sustainability and costeffectiveness.. Category: Miscellaneous. Subcategory: Software. Organization: Ministry of Information and Broadcasting. Description: The Directorate of Publications Division DPD is a publication agency of the Government of India that is engaged in publishing books and journals in various languages and for varied sections of people. DPD provides a platform for buyers to purchase books and journals through its sales emporiums and various online platforms. Employment News EN, now a part of DPD, is a journal that provides information on employment opportunities and is published in English, Hindi , and Urdu and is also available online on a subscription basis. The various online platforms present for purchasing DPD booksebooksjournalsejournals are S. No. Name of platform URL 1 Publication Division Website httpswww.publicationsdivision.nic.in 2 Journals Website with archives httpswww.publicationsdivision.nic.injournals 3 Employment News Website Hindi httpwww.employmentnews.gov.in 4 Employment News Website English httpwww.employmentnews.gov.in 5 DPD Android app httpsplay.google.comstoreappsdetailsidcom.iconmaandhlen_INandglUS 6 Employment News Portal under development 7 Amazon 8 Various Social Media Platforms like Facebook, Twitter and Instagram TwitterInstagram DPD_India, Twitter YojanaJournal, Employ_News Facebook httpswww.facebook.compublicationsdivision 5 Despite being present on multiple online platforms the sales figures for the books and journals published by DPD over the years have not shown encouraging growth. While initiatives are being taken from editorial and design teams to improve the books and journals, a need was felt to encourage young students graduates to work on providing complete digital solutions and reworking a complete digital marking strategy for increasing online sales. By making use of various tools for digital marketing, the products of DPD may be available to the buyers seamlessly on various platforms. These may include email marketing, Content marketing, competitor analysis, Search Engine optimization, performance reporting, Social media and website analytics and the like. Details of DM Tools that may be used  The SEO may improve the chances of DPD websites figuring out among the top options for purchasing books in India and abroad related to the content published. Specific books on Gandhiji, PM, President, Rashtrapati Bhawan and even Mirza Galib, Lord Buddha, etc., published by DPD may also be available when users search for such books personalities. Moreover, DPD has a database of user details but presently there is no system of capturing such data for increasing sales and retaining customers. As such email marketing may be employed to retain one time or regular buyers by providing them targeted options for purchase as per their preferences. Alerts for new issues of a preferred journal may also be sent to them. Also, solution to capture new audience may also be worked out using email marketing. Next, DPDEN are present on various social media platforms like Twitter, Instagram, Facebook etc. and various tools may be used to best make use of such social media platforms for increase sales and capturing new customersbuyers for which analytics, advertising, content marketing and other tools may be explored. If needed, any new platform that may be required from point of view of improving online sales may also be created. Objective  A push through various digital media tools is targeted at increasing sales of various books and journals of DPD and encouraging bookreading culture in the country for various socioeconomic issues. A range of literature published by DPD is based on preservation on historic and cultural heritage of India and promotion of reading habits in children. Digital media promotion should pay special attention to these aspects. The sales figures for Yojana and Kurukshetra Magazine in EnglishHindi as well as in various regional languages have a huge scope for improving the sales and needs to be given special attention especially since the audience includes civil services aspirants in various parts of the country. Giving a boost to the subscriptions of Employment News is another focus area for which there is a huge scope in smaller cities and towns. Prototypes to be created by the participating teams  The participating teams may create the following subject to subject to suitability, sustainability and costeffectiveness of the DM tools 6 1. A complete DM strategy for a increasing online sale of books and journals b increasing subscriptions of Employment News 2. A prototype for SEO using various keywords aimed at increasing visibility of website and apps, amazon books of DPD 3. At least 45 prototypes for content marketing A prototype for email marketing for a capturing onetime buyers b capturing new audience 4. A model for promoting sales through various social media platforms 5. A model for increasing sales of Yojana and Kurukshetra Magazines using suitable DM tools"}
{"id": "SIH1330", "title": "Text to Video of various PIB Press Releases using Artificial Intelligence  Machine Learning  Generative Adversarial Networks in English and 13 Regional Languages viz. Hindi, Urdu, Punjabi, Gujarati, Marathi, Telugu, Kannada, Malayalam, Tamil, Odia, Bengali, Assamese and Manipuri.", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Information and Broadcasting", "text": "Title: Text to Video of various PIB Press Releases using Artificial Intelligence  Machine Learning  Generative Adversarial Networks in English and 13 Regional Languages viz. Hindi, Urdu, Punjabi, Gujarati, Marathi, Telugu, Kannada, Malayalam, Tamil, Odia, Bengali, Assamese and Manipuri.. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Information and Broadcasting. Description: Press Releases of the Press Information Bureau are in the form of text. The attention span of the user is reducing by the year, So to engage with the user in a meaningful way, the Press Releases need to be provided in a video format. Software should be designed in such a format to generate the videos automatically from the Press Releases published. The images and clips used for generating the video should be copyright free. There should be a provision to store a pool of images and clips for generating the video. The generated video should be vetted by the concerned PIB officer before publishing. Software should be designed in such a format to generate the videos automatically from the Press Releases published. The images and clips used for generating the video should be copyrightfree and authentic sources. There should be a provision to store a pool of images and clips for generating the video. The generated video should be vetted by the concerned PIB officer before publishing. The software also includes a provision to send the notification to the concerned PIB officer for approval. After approval, the software should have the feature of autouploading on the concerned social media sites."}
{"id": "SIH1329", "title": "360degree feedback software for the Government of India related News Stories in Regional Media using Artificial Intelligence  Machine Learning", "category": "Smart Automation", "subcategory": "Software", "organization": "Ministry of Information and Broadcasting", "text": "Title: 360degree feedback software for the Government of India related News Stories in Regional Media using Artificial Intelligence  Machine Learning. Category: Smart Automation. Subcategory: Software. Organization: Ministry of Information and Broadcasting. Description: The Press Information Bureau PIB is the nodal agency of the Government of India to disseminate information on government policies, programs, initiatives, and achievements to the print and electronic media. It functions as an interface between the Government and the media and provides feedback to the Government on people\u00c3\u0192\u00c2\u00c3\u00e2\u0161\u00c2\u00c3\u00e2\u017e\u00c2s reactions as reflected in the media. Information is disseminated from Hqrs through Press Releases in English, Hindi, and Urdu and subsequently translated from PIB Regional offices into other Indian languages like Punjabi, Gujarati, Marathi, Telugu, Kannada, Malayalam, Tamil, Odia, Bengali, Assamese, and Manipuri, to reach out to about 8,400 newspapers and media organizations across the country. To provide effective and timely feedback to the Government, an automated feedback system for all the above regional languages using Artificial Intelligence  Machine Learning is required. The feedback system should crawl the select regional media sites around 200 websites for the news published in regional languages. The software should categorize the stories into the concerned departments as per the tags provided. The stories should be categorized as favorable positive, neutral, or not favorable negative to the Government of India. Negative stories pertaining to a department should be notified to the concerned PIB officer on a realtime basis by SMS or Android notification or by other means. Epapers of select newspapers should be scanned by the system automatically using an Optical Character Recognition OCR. The concerned news clippings if it pertains to the Government of India should be cut and electronically pasted in a predesigned template mentioning the name of the newspaper, the page number where the story was published, the name of the edition, etc. These clippings should be classified into Departments and tonality positive, negative, and neutral. Also in the dashboard, the title of the newspapers should be displayed and the stories should be in a position to be sortedfiltered using the variables like Tonality, Edition, etc. The system should also crawl through the YouTube channels of select news channels and identify the portion of the news bulletin pertaining to the Government of India using closed captioning. If closed captioning is not available, should use audio to text feature to capture the transcript. Once the portion of the video is identified, the said video has to be categorized into Departments and tonality. If the story is negative, the concerned PIB officer should get the notification immediately."}
{"id": "SIH1328", "title": "Call for costeffective ways of making water source for piped drinking water supply sustainable in Rural areas", "category": "Renewable  Sustainable Energy", "subcategory": "Hardware", "organization": "Ministry of Jal Shakti", "text": "Title: Call for costeffective ways of making water source for piped drinking water supply sustainable in Rural areas. Category: Renewable  Sustainable Energy. Subcategory: Hardware. Organization: Ministry of Jal Shakti. Description: Jal Jeevan Mission was launched with the vision of providing drinking water in adequate quantity of prescribed quality on regular and longterm basis to every rural household. A comprehensive costeffective technology is needed for the assessment of groundwater recharge done by source sustainability technology which is positively affecting the ground waterbased source under consideration and predicting the longevity of the drinking water source to serve at the design discharge in the longterm considering the effect of drawl by the irrigation tube well. The technology should focus on the following a. Monitoring of data for monitoring of drinking water source for a village. Generation of alerts in case of rapid deterioration. b. Innovative costeffective technology for Rain Water Harvesting and Recharge. c. Innovative means to improve source sustainability. d. Reuse of Grey Water Utilize nontoxic wastewater from householdswithwithout basic treatment e. Accurate measurement of the capacity of the Aquifer and the actual water demand. f. Citing correct locations for recharge and discharge of ground water. B. Potential condition of Water Quality."}
{"id": "SIH1327", "title": "Developing a system for Patient Care in the Health Sector", "category": "Robotics and Drones", "subcategory": "Hardware", "organization": "Government of Kerala", "text": "Title: Developing a system for Patient Care in the Health Sector. Category: Robotics and Drones. Subcategory: Hardware. Organization: Government of Kerala. Description: As the demand for healthcare services continues to increase, healthcare professionals are facing challenges in meeting the needs of patients while maintaining quality care. One of the major challenges is providing 247 care for patients who require continuous monitoring and assistance.The challenge for this hackathon would be to develop innovative and effective solutions for designing and building robots for patient care in the health sector. Participants would need to consider various factors such as safety, reliability, ease of use, and affordability, while also ensuring that the robots are designed to meet the unique needs of patients in different healthcare settings."}
{"id": "SIH1326", "title": "Ideate and implement a system to enhance the quality of education in rural areas.", "category": "Smart Education", "subcategory": "Software", "organization": "Government of Kerala", "text": "Title: Ideate and implement a system to enhance the quality of education in rural areas.. Category: Smart Education. Subcategory: Software. Organization: Government of Kerala. Description: Ideate and implement a system to enhance the quality of education in rural areas. The aim of the system should not only focus on increasing the literacy rate but also should assist to elevate the communication skills and knowledge of the targeted society. The system should offer   Study materials and mentor access.  Monitoring skill progress  Bridge the digital divide  Provide information about grants, loans and incentives.  Offer connectivity to financially disadvantage patrons.  Help individuals with employment opportunities.  Research and development  Access to material resources"}
{"id": "SIH1325", "title": "AI Assisted Telemedicine KIOSK for Rural India", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Software", "organization": "Government of Kerala", "text": "Title: AI Assisted Telemedicine KIOSK for Rural India. Category: Agriculture, FoodTech  Rural Development. Subcategory: Software. Organization: Government of Kerala. Description: Health care in rural India is still an unresolved area that demands improved and innovative solutions. The easy availability and access to expert doctors, according to the medical condition of the individualpatient can be provided by the AIassisted telemedicine robotic Kiosk that can be set up anywhere in the village. Individuals may mark their identity through the biometric scanner. A robot may speak to the individual, enquiring about the illness. Later, the individual will be directed online to an expert doctor, via esanjeevani App. After the consultation, medicines and other associated services can be provided to them through the local Asha worker without any delay."}
{"id": "SIH1324", "title": "Air and water quality index and environment monitoring", "category": "Agriculture, FoodTech  Rural Development", "subcategory": "Software", "organization": "Government of Kerala", "text": "Title: Air and water quality index and environment monitoring. Category: Agriculture, FoodTech  Rural Development. Subcategory: Software. Organization: Government of Kerala. Description: Considering the importance of air and water to human existence, air pollution and water pollution are critical issues that require collective effort for prevention and control. Different types of anthropogenic activities have resulted in environmental dilapidation and ruin. One of the tools that can be used for such a campaign is Air Quality Index AQI. The AQI was based on the concentrations of different pollutants We are also familiar with the Water Quality Index WQI, which in simple terms tells what the quality of drinking water is from a drinking water supply. There is a need for constant and continuous environment monitoring of air quality and water quality for the development of AQI and WQI, which in turn will enable clear communication of how clean or unhealthy the air and water in the study area is."}
